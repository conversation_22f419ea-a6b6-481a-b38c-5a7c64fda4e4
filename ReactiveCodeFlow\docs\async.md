# Async Support: Functional Asynchronous Programming

ReactiveCodeFlow provides comprehensive support for asynchronous operations using functional programming patterns, enabling efficient handling of I/O-bound operations, API calls, and concurrent processing.

## Async Architecture

### Async Node Definitions

Async nodes use coroutine functions for all phases:

```python
@dataclass(frozen=True)
class AsyncNodeDefinition:
    name: str
    prep_fn: Callable[[ExecutionContext], Awaitable[Result[Any, E]]]
    exec_fn: Callable[[Any], Awaitable[Result[T, E]]]
    post_fn: Callable[[ExecutionContext, Any, T], Awaitable[Result[tuple[Action, SharedState], E]]]
    fallback_fn: Callable[[Any, E], Awaitable[Result[T, E]]] | None = None
    retry_config: RetryConfig = RetryConfig()
```

### Creating Async Nodes

```python
from reactivecodeflow.core.async_support import create_async_node
import asyncio
import aiohttp

def create_api_client_node():
    """Create an async node for API calls."""
    
    async def prep_fn(ctx):
        """Prepare API request parameters."""
        await asyncio.sleep(0.001)  # Simulate async prep
        
        if ctx.shared_state.contains_key("api_config"):
            config = ctx.shared_state.get("api_config")
            return Ok(config)
        return Error("Missing API configuration")
    
    async def exec_fn(config):
        """Execute async API call."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(config["url"]) as response:
                    if response.status == 200:
                        data = await response.json()
                        return Ok(data)
                    else:
                        return Error(f"API call failed: {response.status}")
        except Exception as e:
            return Error(f"Network error: {str(e)}")
    
    async def post_fn(ctx, prep_res, exec_res):
        """Process API response."""
        await asyncio.sleep(0.001)  # Simulate async post-processing
        
        # Store response in state
        updated_state = ctx.shared_state.add("api_response", exec_res)
        return Ok(("process_response", updated_state))
    
    return create_async_node("api_client", prep_fn, exec_fn, post_fn)
```

## Async Execution

### Single Async Node Execution

```python
from reactivecodeflow.core.async_support import execute_async_node

async def run_async_node():
    """Execute an async node."""
    
    # Create async node
    api_node = create_api_client_node()
    
    # Create context
    context = ExecutionContext(
        shared_state=Map.of_dict({
            "api_config": {
                "url": "https://api.example.com/data",
                "timeout": 30
            }
        }),
        params=Map.empty(),
        retry_config=RetryConfig(max_retries=3)
    )
    
    # Execute async node
    result = await execute_async_node(api_node, context)
    
    if result.is_ok():
        node_result = result.ok
        print(f"API response: {node_result.value}")
        print(f"Execution time: {node_result.execution_time}")
    else:
        print(f"Error: {result.error}")

# Run the async operation
asyncio.run(run_async_node())
```

### Async Batch Processing

```python
def create_async_batch_processor():
    """Create an async batch node for parallel processing."""
    
    async def prep_fn(ctx):
        """Prepare batch of items for processing."""
        if ctx.shared_state.contains_key("items"):
            items = ctx.shared_state.get("items")
            return Ok(Seq.of_iterable(items))
        return Error("No items to process")
    
    async def exec_fn(item):
        """Process single item asynchronously."""
        try:
            # Simulate async processing
            await asyncio.sleep(0.1)
            processed = f"processed_{item}"
            return Ok(processed)
        except Exception as e:
            return Error(f"Processing failed: {e}")
    
    async def post_fn(ctx, prep_res, exec_results):
        """Aggregate async results."""
        await asyncio.sleep(0.01)
        
        # Convert results to list
        results_list = list(exec_results)
        updated_state = ctx.shared_state.add("processed_items", results_list)
        return Ok(("complete", updated_state))
    
    return create_async_batch_node(
        "async_batch_processor",
        prep_fn,
        exec_fn,
        post_fn,
        parallel=True  # Enable parallel processing
    )
```

## Async Retry Mechanisms

### Async Retry with Backoff

```python
async def with_async_retry(
    exec_fn: Callable[[Any], Awaitable[Result[T, E]]],
    fallback_fn: Callable[[Any, E], Awaitable[Result[T, E]]] | None,
    retry_config: RetryConfig,
    current_retry: int = 0
) -> Callable[[Any], Awaitable[Result[T, E]]]:
    """Create async retry wrapper with exponential backoff."""
    
    async def retry_wrapper(prep_result: Any) -> Result[T, E]:
        result = await exec_fn(prep_result)
        
        if result.is_ok():
            return result
        else:
            error = result.error
            if current_retry < retry_config.max_retries - 1:
                # Calculate wait time with exponential backoff
                wait_time = retry_config.wait_seconds * (retry_config.backoff_multiplier ** current_retry)
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
                
                # Recursive retry
                next_retry_fn = await with_async_retry(exec_fn, fallback_fn, retry_config, current_retry + 1)
                return await next_retry_fn(prep_result)
            else:
                # All retries exhausted, try fallback
                if fallback_fn:
                    return await fallback_fn(prep_result, error)
                else:
                    return result
    
    return retry_wrapper
```

### Async Fallback Patterns

```python
def create_resilient_async_node():
    """Create an async node with comprehensive error handling."""
    
    async def prep_fn(ctx):
        return Ok(ctx.shared_state.get("input_data"))
    
    async def exec_fn(data):
        """Primary async operation that might fail."""
        try:
            # Simulate unreliable async operation
            await asyncio.sleep(0.1)
            if random.random() < 0.3:  # 30% failure rate
                return Error("Random failure")
            return Ok(f"processed_{data}")
        except Exception as e:
            return Error(str(e))
    
    async def post_fn(ctx, prep_res, exec_res):
        updated_state = ctx.shared_state.add("result", exec_res)
        return Ok(("success", updated_state))
    
    async def fallback_fn(prep_result, error):
        """Fallback operation when primary fails."""
        await asyncio.sleep(0.05)  # Faster fallback
        return Ok(f"fallback_result_{prep_result}")
    
    return create_async_node(
        "resilient_async",
        prep_fn,
        exec_fn,
        post_fn,
        fallback_fn=fallback_fn,
        retry_config=RetryConfig(max_retries=3, wait_seconds=0.1)
    )
```

## Concurrent Processing Patterns

### Parallel Async Operations

```python
async def parallel_async_processing(items: list):
    """Process multiple items in parallel."""
    
    async def process_item(item):
        """Process a single item asynchronously."""
        await asyncio.sleep(0.1)  # Simulate async work
        return f"processed_{item}"
    
    # Create tasks for parallel execution
    tasks = [process_item(item) for item in items]
    
    # Wait for all tasks to complete
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Handle results and exceptions
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            processed_results.append(Error(f"Item {i} failed: {result}"))
        else:
            processed_results.append(Ok(result))
    
    return processed_results

# Usage
async def run_parallel_processing():
    items = ["item1", "item2", "item3", "item4"]
    results = await parallel_async_processing(items)
    
    for i, result in enumerate(results):
        if result.is_ok():
            print(f"Item {i}: {result.ok}")
        else:
            print(f"Item {i} failed: {result.error}")
```

### Async Pipeline Processing

```python
async def create_async_pipeline():
    """Create an async processing pipeline."""
    
    async def stage1(data):
        """First stage of async pipeline."""
        await asyncio.sleep(0.05)
        return Ok(f"stage1_{data}")
    
    async def stage2(data):
        """Second stage of async pipeline."""
        await asyncio.sleep(0.05)
        return Ok(f"stage2_{data}")
    
    async def stage3(data):
        """Third stage of async pipeline."""
        await asyncio.sleep(0.05)
        return Ok(f"stage3_{data}")
    
    async def pipeline(input_data):
        """Execute the complete async pipeline."""
        
        # Stage 1
        result1 = await stage1(input_data)
        if result1.is_error():
            return result1
        
        # Stage 2
        result2 = await stage2(result1.ok)
        if result2.is_error():
            return result2
        
        # Stage 3
        result3 = await stage3(result2.ok)
        return result3
    
    return pipeline

# Usage
async def run_pipeline():
    pipeline = await create_async_pipeline()
    result = await pipeline("input_data")
    
    if result.is_ok():
        print(f"Pipeline result: {result.ok}")
    else:
        print(f"Pipeline failed: {result.error}")
```

## Async Resource Management

### Async Context Managers

```python
class AsyncResourceManager:
    """Async context manager for resource lifecycle."""
    
    def __init__(self, resource_config):
        self.config = resource_config
        self.resource = None
    
    async def __aenter__(self):
        """Acquire resource asynchronously."""
        try:
            self.resource = await self.acquire_resource()
            return self.resource
        except Exception as e:
            raise RuntimeError(f"Failed to acquire resource: {e}")
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Release resource asynchronously."""
        if self.resource:
            await self.release_resource()
    
    async def acquire_resource(self):
        """Acquire the actual resource."""
        await asyncio.sleep(0.01)  # Simulate async acquisition
        return f"resource_{self.config['name']}"
    
    async def release_resource(self):
        """Release the actual resource."""
        await asyncio.sleep(0.01)  # Simulate async cleanup

# Usage in async node
def create_resource_managed_node():
    async def prep_fn(ctx):
        return Ok(ctx.shared_state.get("resource_config"))
    
    async def exec_fn(config):
        async with AsyncResourceManager(config) as resource:
            # Use resource for async operations
            await asyncio.sleep(0.1)
            result = f"processed_with_{resource}"
            return Ok(result)
    
    async def post_fn(ctx, prep_res, exec_res):
        updated_state = ctx.shared_state.add("result", exec_res)
        return Ok(("complete", updated_state))
    
    return create_async_node("resource_managed", prep_fn, exec_fn, post_fn)
```

## Async Flow Integration

### Mixed Sync/Async Flows

```python
# Note: In practice, you would need separate execution paths for sync and async nodes
# This is a conceptual example of how they might be integrated

async def execute_mixed_flow(flow_def, context):
    """Execute a flow with both sync and async nodes."""
    
    current_node_name = flow_def.start_node
    current_context = context
    
    while current_node_name:
        node_def = flow_def.get_node(current_node_name)
        
        if isinstance(node_def, AsyncNodeDefinition):
            # Execute async node
            result = await execute_async_node(node_def, current_context)
        else:
            # Execute sync node
            result = execute_node(node_def, current_context)
        
        if result.is_error():
            return result
        
        node_result = result.ok
        current_context = ExecutionContext(
            shared_state=node_result.updated_state,
            params=current_context.params,
            retry_config=current_context.retry_config
        )
        
        # Get next node based on action
        next_node = get_next_node_name(flow_def, current_node_name, node_result.action)
        current_node_name = next_node.value if next_node.is_some() else None
    
    return Ok(current_context.shared_state)
```

## Testing Async Code

### Async Unit Testing

```python
import pytest

@pytest.mark.asyncio
async def test_async_node():
    """Test async node execution."""
    
    # Create async node
    node = create_api_client_node()
    
    # Create test context
    context = ExecutionContext(
        shared_state=Map.of_dict({
            "api_config": {"url": "https://httpbin.org/json"}
        }),
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    # Execute async node
    result = await execute_async_node(node, context)
    
    # Assertions
    assert result.is_ok()
    node_result = result.ok
    assert node_result.action == "process_response"
    assert node_result.updated_state.contains_key("api_response")

@pytest.mark.asyncio
async def test_async_retry_mechanism():
    """Test async retry functionality."""
    
    attempt_count = 0
    
    async def failing_operation(data):
        nonlocal attempt_count
        attempt_count += 1
        if attempt_count < 3:
            return Error(f"Attempt {attempt_count} failed")
        return Ok("success_after_retries")
    
    retry_fn = await with_async_retry(
        failing_operation,
        None,
        RetryConfig(max_retries=3, wait_seconds=0.01)
    )
    
    result = await retry_fn("test_data")
    
    assert result.is_ok()
    assert result.ok == "success_after_retries"
    assert attempt_count == 3

@pytest.mark.asyncio
async def test_parallel_processing():
    """Test parallel async processing."""
    
    items = ["item1", "item2", "item3"]
    results = await parallel_async_processing(items)
    
    assert len(results) == 3
    for result in results:
        assert result.is_ok()
        assert result.ok.startswith("processed_")
```

### Async Performance Testing

```python
import time

@pytest.mark.asyncio
async def test_async_performance():
    """Test async operation performance."""
    
    start_time = time.time()
    
    # Sequential execution
    sequential_results = []
    for i in range(5):
        result = await async_operation(f"item_{i}")
        sequential_results.append(result)
    
    sequential_time = time.time() - start_time
    
    # Parallel execution
    start_time = time.time()
    
    tasks = [async_operation(f"item_{i}") for i in range(5)]
    parallel_results = await asyncio.gather(*tasks)
    
    parallel_time = time.time() - start_time
    
    # Parallel should be significantly faster
    assert parallel_time < sequential_time * 0.8
    assert len(parallel_results) == 5

async def async_operation(item):
    """Simulate async operation."""
    await asyncio.sleep(0.1)
    return f"processed_{item}"
```

## Best Practices

1. **Use Async for I/O-Bound Operations**: Network calls, file operations, database queries
2. **Avoid Blocking Operations**: Don't use synchronous operations in async functions
3. **Handle Timeouts**: Always set timeouts for external async operations
4. **Resource Management**: Use async context managers for resource cleanup
5. **Error Handling**: Wrap async operations in try/except blocks
6. **Concurrency Control**: Use semaphores to limit concurrent operations
7. **Testing**: Use pytest-asyncio for comprehensive async testing
8. **Performance Monitoring**: Monitor async operation performance and bottlenecks

## Common Pitfalls

### Avoiding Common Mistakes

```python
# ❌ Wrong: Blocking operation in async function
async def bad_async_function():
    time.sleep(1)  # Blocks the event loop
    return "result"

# ✅ Correct: Non-blocking async operation
async def good_async_function():
    await asyncio.sleep(1)  # Non-blocking
    return "result"

# ❌ Wrong: Not awaiting async operations
async def bad_async_chain():
    result1 = async_operation_1()  # Missing await
    result2 = async_operation_2(result1)  # Will fail
    return result2

# ✅ Correct: Properly awaiting async operations
async def good_async_chain():
    result1 = await async_operation_1()
    result2 = await async_operation_2(result1)
    return result2
```

This comprehensive async support enables ReactiveCodeFlow to handle modern asynchronous workloads efficiently while maintaining the benefits of functional programming and immutable data structures.
