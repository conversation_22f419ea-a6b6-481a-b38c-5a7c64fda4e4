"""
Hello World example demonstrating ReactiveCodeFlow functional programming approach.

This example shows how to create and execute a simple workflow using pure functions
and immutable data structures, contrasting with the original PocketFlow OOP approach.
"""

from reactivecodeflow.core.types import ExecutionContext, RetryConfig
from reactivecodeflow.core.node import create_node, execute_node
from reactivecodeflow.core.flow import create_flow, add_node, execute_flow
from expression import Ok, Error
from expression.collections import Map


def create_question_node():
    """
    Create a node that prepares a question for processing.
    
    This demonstrates the functional approach to node creation using pure functions.
    """
    def prep_fn(ctx):
        """Extract question from shared state."""
        question = ctx.shared_state.get("question")
        return question.map(Ok).value_or(Error("No question provided"))
    
    def exec_fn(question):
        """Process the question (identity function for this example)."""
        return Ok(question)
    
    def post_fn(ctx, prep_res, exec_res):
        """Store processed question and determine next action."""
        updated_state = ctx.shared_state.add("processed_question", exec_res)
        return Ok(("answer", updated_state))
    
    return create_node("question", prep_fn, exec_fn, post_fn)


def create_answer_node():
    """
    Create a node that generates an answer to the question.
    
    This demonstrates functional composition and immutable state management.
    """
    def prep_fn(ctx):
        """Get the processed question."""
        question = ctx.shared_state.get("processed_question")
        return question.map(Ok).value_or(Error("No processed question found"))
    
    def exec_fn(question):
        """Generate answer using a simple transformation."""
        if "universe" in question.lower():
            return Ok("42 - The answer to the ultimate question of life, the universe, and everything!")
        elif "hello" in question.lower():
            return Ok("Hello there! How can I help you today?")
        else:
            return Ok(f"That's an interesting question: '{question}'. Let me think about it...")
    
    def post_fn(ctx, prep_res, exec_res):
        """Store the answer in shared state."""
        updated_state = ctx.shared_state.add("answer", exec_res)
        return Ok(("complete", updated_state))
    
    return create_node("answer", prep_fn, exec_fn, post_fn)


def create_qa_flow():
    """
    Create a question-answering flow using functional composition.
    
    This demonstrates how flows are built using immutable data structures
    and pure functional composition.
    """
    # Create nodes
    question_node = create_question_node()
    answer_node = create_answer_node()
    
    # Create flow with functional composition
    flow = (create_flow("qa_flow", "question")
            .pipe(lambda f: add_node(f, question_node))
            .pipe(lambda f: add_node(f, answer_node))
            .pipe(lambda f: add_transition(f, "question", "answer", "answer")))
    
    return flow


def demonstrate_functional_qa():
    """
    Demonstrate the functional Q&A workflow.
    
    This shows how the entire system works with immutable data and pure functions.
    """
    print("=== ReactiveCodeFlow Functional Q&A Demo ===\n")
    
    # Create the flow
    qa_flow = create_qa_flow()
    
    # Test questions
    test_questions = [
        "What is the meaning of the universe?",
        "Hello, how are you?",
        "What is functional programming?"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"Question {i}: {question}")
        
        # Create immutable initial state
        initial_state = Map.of_dict({
            "question": question,
            "processed_question": None,
            "answer": None
        })
        
        # Create execution context
        context = ExecutionContext(
            shared_state=initial_state,
            params=Map.empty(),
            retry_config=RetryConfig(max_retries=3, wait_seconds=0.1)
        )
        
        # Execute flow functionally
        result = execute_flow(qa_flow, context)
        
        match result:
            case Ok(flow_result):
                answer = flow_result.final_state.get("answer")
                match answer:
                    case Some(ans):
                        print(f"Answer: {ans}")
                        print(f"Execution path: {' → '.join(flow_result.execution_path)}")
                        print(f"Total time: {flow_result.total_execution_time:.3f}s")
                    case Nothing():
                        print("No answer generated")
            case Error(error):
                print(f"Error: {error}")
        
        print("-" * 50)


def compare_with_original():
    """
    Compare the functional approach with the original OOP approach.
    
    This demonstrates the architectural differences and benefits.
    """
    print("\n=== Architectural Comparison ===\n")
    
    print("Original PocketFlow (OOP):")
    print("""
    class AnswerNode(Node):
        def prep(self, shared):
            return shared["question"]
        
        def exec(self, question):
            return generate_answer(question)
        
        def post(self, shared, prep_res, exec_res):
            shared["answer"] = exec_res  # Mutable state!
    
    # Mutable shared state
    shared = {"question": "What is life?", "answer": None}
    answer_node = AnswerNode()
    qa_flow = Flow(start=answer_node)
    qa_flow.run(shared)  # Modifies shared in-place
    """)
    
    print("\nReactiveCodeFlow (Functional):")
    print("""
    def create_answer_node():
        def prep_fn(ctx):
            return ctx.shared_state.get("question").map(Ok).value_or(Error("No question"))
        
        def exec_fn(question):
            return Ok(generate_answer(question))
        
        def post_fn(ctx, prep_res, exec_res):
            updated_state = ctx.shared_state.add("answer", exec_res)  # Immutable!
            return Ok(("complete", updated_state))
        
        return create_node("answer", prep_fn, exec_fn, post_fn)
    
    # Immutable shared state
    initial_state = Map.of_dict({"question": "What is life?", "answer": None})
    context = ExecutionContext(shared_state=initial_state, ...)
    result = execute_flow(qa_flow, context)  # Returns new state
    """)
    
    print("\nKey Differences:")
    print("1. Immutability: All data structures are immutable")
    print("2. Pure Functions: No side effects, easier to test and reason about")
    print("3. Error Handling: Railway-oriented programming with Result types")
    print("4. Composability: Functions compose naturally for complex workflows")
    print("5. Type Safety: Comprehensive type checking with Expression library")


if __name__ == "__main__":
    # Import required modules for the example
    from expression import Some, Nothing
    from reactivecodeflow.core.flow import add_transition
    
    # Run the demonstration
    demonstrate_functional_qa()
    compare_with_original()
