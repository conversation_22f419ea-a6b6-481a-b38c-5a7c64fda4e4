"""
Async support for ReactiveCodeFlow using Expression's async effects.

This module provides asynchronous execution capabilities while maintaining
functional programming principles and immutability.
"""

import asyncio
import time
from typing import Any, Callable, TypeVar, Awaitable, Generic
from collections.abc import AsyncGenerator
from dataclasses import dataclass

from expression import Option, Result, Some, Nothing, Ok, Error
from expression import effect
from expression.collections import Map, Block, Seq

from .types import (
    NodeDefinition, BatchNodeDefinition, FlowDefinition, ExecutionContext,
    NodeResult, FlowResult, SharedState, Action, RetryConfig, T, E
)

# Type variables
T = TypeVar('T')
E = TypeVar('E')

@dataclass(frozen=True)
class AsyncNodeDefinition(Generic[T, E]):
    """Immutable async node definition using pure async functions."""
    name: str
    prep_fn: Callable[[ExecutionContext], Awaitable[Result[Any, E]]]
    exec_fn: Callable[[Any], Awaitable[Result[T, E]]]
    post_fn: Callable[[ExecutionContext, Any, T], Awaitable[Result[tuple[Action, SharedState], E]]]
    fallback_fn: Callable[[Any, E], Awaitable[Result[T, E]]] | None = None
    retry_config: RetryConfig = RetryConfig()


@dataclass(frozen=True)
class AsyncBatchNodeDefinition(Generic[T, E]):
    """Immutable async batch node definition."""
    name: str
    prep_fn: Callable[[ExecutionContext], Awaitable[Result[Seq[Any], E]]]
    exec_fn: Callable[[Any], Awaitable[Result[T, E]]]
    post_fn: Callable[[ExecutionContext, Seq[Any], Seq[T]], Awaitable[Result[tuple[Action, SharedState], E]]]
    fallback_fn: Callable[[Any, E], Awaitable[Result[T, E]]] | None = None
    retry_config: RetryConfig = RetryConfig()
    parallel: bool = False  # Whether to process items in parallel


# Pure function to create async node
def create_async_node(
    name: str,
    prep_fn: Callable[[ExecutionContext], Awaitable[Result[Any, E]]],
    exec_fn: Callable[[Any], Awaitable[Result[T, E]]],
    post_fn: Callable[[ExecutionContext, Any, T], Awaitable[Result[tuple[Action, SharedState], E]]],
    fallback_fn: Callable[[Any, E], Awaitable[Result[T, E]]] | None = None,
    retry_config: RetryConfig = RetryConfig()
) -> AsyncNodeDefinition[T, E]:
    """Create an immutable async node definition."""
    return AsyncNodeDefinition(
        name=name,
        prep_fn=prep_fn,
        exec_fn=exec_fn,
        post_fn=post_fn,
        fallback_fn=fallback_fn,
        retry_config=retry_config
    )


def create_async_batch_node(
    name: str,
    prep_fn: Callable[[ExecutionContext], Awaitable[Result[Seq[Any], E]]],
    exec_fn: Callable[[Any], Awaitable[Result[T, E]]],
    post_fn: Callable[[ExecutionContext, Seq[Any], Seq[T]], Awaitable[Result[tuple[Action, SharedState], E]]],
    fallback_fn: Callable[[Any, E], Awaitable[Result[T, E]]] | None = None,
    retry_config: RetryConfig = RetryConfig(),
    parallel: bool = False
) -> AsyncBatchNodeDefinition[T, E]:
    """Create an immutable async batch node definition."""
    return AsyncBatchNodeDefinition(
        name=name,
        prep_fn=prep_fn,
        exec_fn=exec_fn,
        post_fn=post_fn,
        fallback_fn=fallback_fn,
        retry_config=retry_config,
        parallel=parallel
    )


# Async retry logic
async def with_async_retry(
    exec_fn: Callable[[Any], Awaitable[Result[T, E]]],
    fallback_fn: Callable[[Any, E], Awaitable[Result[T, E]]] | None,
    retry_config: RetryConfig,
    current_retry: int = 0
) -> Callable[[Any], Awaitable[Result[T, E]]]:
    """Create a retry-enabled version of an async execution function."""
    async def retry_wrapper(prep_result: Any) -> Result[T, E]:
        result = await exec_fn(prep_result)
        
        if result.is_ok():
            return result
        else:
            error = result.error
            if current_retry < retry_config.max_retries - 1:
                # Wait with exponential backoff
                wait_time = retry_config.wait_seconds * (retry_config.backoff_multiplier ** current_retry)
                if wait_time > 0:
                    await asyncio.sleep(wait_time)

                # Recursive retry with incremented counter
                next_retry_fn = await with_async_retry(exec_fn, fallback_fn, retry_config, current_retry + 1)
                return await next_retry_fn(prep_result)
            else:
                # All retries exhausted, try fallback
                if fallback_fn:
                    return await fallback_fn(prep_result, error)
                else:
                    return result
    
    return retry_wrapper


# Async node execution using simple async functions
async def execute_async_node(
    node_def: AsyncNodeDefinition[T, E],
    context: ExecutionContext
) -> Result[NodeResult[T], str]:
    """Execute an async node using functional async effects."""
    start_time = time.time()

    try:
        # Preparation phase
        prep_result = await node_def.prep_fn(context)
        if prep_result.is_error():
            return Error(f"Prep phase failed: {prep_result.error}")
        prep_data = prep_result.ok

        # Execution phase with async retry logic
        retry_enabled_exec = await with_async_retry(
            node_def.exec_fn,
            node_def.fallback_fn,
            node_def.retry_config
        )
        exec_result = await retry_enabled_exec(prep_data)
        if exec_result.is_error():
            return Error(f"Exec phase failed: {exec_result.error}")
        exec_data = exec_result.ok

        # Post-processing phase
        post_result = await node_def.post_fn(context, prep_data, exec_data)
        if post_result.is_error():
            return Error(f"Post phase failed: {post_result.error}")
        action, updated_state = post_result.ok

        execution_time = time.time() - start_time

        return Ok(NodeResult(
            value=exec_data,
            action=action,
            updated_state=updated_state,
            execution_time=execution_time
        ))

    except Exception as e:
        return Error(f"Async node execution failed: {str(e)}")


# Async batch node execution
async def execute_async_batch_node(
    batch_node_def: AsyncBatchNodeDefinition[T, E],
    context: ExecutionContext
) -> Result[NodeResult[Seq[T]], str]:
    """Execute an async batch node with optional parallel processing."""
    start_time = time.time()
    
    # Preparation phase
    prep_items = yield await batch_node_def.prep_fn(context)
    
    # Execution phase
    retry_enabled_exec = await with_async_retry(
        batch_node_def.exec_fn,
        batch_node_def.fallback_fn,
        batch_node_def.retry_config
    )
    
    if batch_node_def.parallel:
        # Parallel execution using asyncio.gather
        tasks = [retry_enabled_exec(item) for item in prep_items]
        exec_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to Error results
        processed_results = []
        for result in exec_results:
            if isinstance(result, Exception):
                processed_results.append(Error(str(result)))
            else:
                processed_results.append(result)
        
        final_results = yield await sequence_async_results(Seq.of_iterable(processed_results))
    else:
        # Sequential execution
        results = []
        for item in prep_items:
            result = yield await retry_enabled_exec(item)
            results.append(result)
        final_results = Seq.of_iterable(results)
    
    # Post-processing phase
    post_result = yield await batch_node_def.post_fn(context, prep_items, final_results)
    action, updated_state = post_result
    
    execution_time = time.time() - start_time
    
    yield NodeResult(
        value=final_results,
        action=action,
        updated_state=updated_state,
        execution_time=execution_time
    )


# Async flow execution
async def execute_async_flow(
    flow_def: FlowDefinition,
    initial_context: ExecutionContext
) -> Result[FlowResult[Any], str]:
    """Execute a flow asynchronously with mixed sync/async nodes."""
    start_time = time.time()
    execution_path = Block.empty()
    current_context = initial_context
    current_node_name = flow_def.start_node
    last_result = None
    
    while True:
        # Get current node definition
        node_def_option = flow_def.get_node(current_node_name)
        
        match node_def_option:
            case Some(node_def):
                # Add current node to execution path
                execution_path = execution_path.cons(current_node_name)
                
                # Execute node (async or sync based on type)
                if isinstance(node_def, AsyncNodeDefinition):
                    node_result = yield await execute_async_node(node_def, current_context)
                else:
                    # Execute sync node in async context
                    from .node import execute_node
                    sync_result = execute_node(node_def, current_context)
                    node_result = yield await sync_result
                
                last_result = node_result.value
                
                # Update context with new state
                current_context = ExecutionContext(
                    shared_state=node_result.updated_state,
                    params=current_context.params,
                    retry_config=current_context.retry_config,
                    current_retry=0
                )
                
                # Find next node
                from .flow import get_next_node_name
                next_node_option = get_next_node_name(flow_def, current_node_name, node_result.action)
                
                match next_node_option:
                    case Some(next_node):
                        current_node_name = next_node
                    case Nothing():
                        break
                        
            case Nothing():
                yield Error(f"Node '{current_node_name}' not found in flow '{flow_def.name}'")
    
    total_time = time.time() - start_time
    
    yield FlowResult(
        final_value=last_result,
        final_action=node_result.action if 'node_result' in locals() else "default",
        final_state=current_context.shared_state,
        execution_path=execution_path.reverse(),
        total_execution_time=total_time
    )


# Helper function for sequencing async results
async def sequence_async_results(results: Seq[Result[T, E]]) -> Result[Seq[T], E]:
    """Convert a sequence of async Results to a Result of sequence."""
    def accumulate(acc: Result[Seq[T], E], result: Result[T, E]) -> Result[Seq[T], E]:
        match (acc, result):
            case (Ok(values), Ok(value)):
                return Ok(values.cons(value))
            case (Error(_), _):
                return acc
            case (_, Error(error)):
                return Error(error)
    
    return results.fold(accumulate, Ok(Seq.empty()))


# Utility for creating simple async nodes
def simple_async_node(
    name: str,
    exec_fn: Callable[[Any], Awaitable[T]],
    prep_fn: Callable[[ExecutionContext], Awaitable[Any]] = lambda ctx: ctx.shared_state,
    post_fn: Callable[[ExecutionContext, Any, T], Awaitable[tuple[Action, SharedState]]] = lambda ctx, prep, exec: ("default", ctx.shared_state)
) -> AsyncNodeDefinition[T, str]:
    """Create a simple async node with default error handling."""
    async def safe_prep(ctx: ExecutionContext) -> Result[Any, str]:
        try:
            return Ok(await prep_fn(ctx))
        except Exception as e:
            return Error(str(e))
    
    async def safe_exec(prep_result: Any) -> Result[T, str]:
        try:
            return Ok(await exec_fn(prep_result))
        except Exception as e:
            return Error(str(e))
    
    async def safe_post(ctx: ExecutionContext, prep_result: Any, exec_result: T) -> Result[tuple[Action, SharedState], str]:
        try:
            return Ok(await post_fn(ctx, prep_result, exec_result))
        except Exception as e:
            return Error(str(e))
    
    return create_async_node(name, safe_prep, safe_exec, safe_post)
