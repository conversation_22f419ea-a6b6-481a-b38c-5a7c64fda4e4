# Types and Data Structures

ReactiveCodeFlow uses immutable data structures from the Expression library to ensure thread safety and predictable behavior.

## Core Types

### ExecutionContext

The execution context carries immutable state through the pipeline:

```python
@dataclass(frozen=True)
class ExecutionContext:
    shared_state: SharedState          # Immutable Map[str, Any]
    params: Map[str, Any]             # Node-specific parameters
    retry_config: RetryConfig         # Retry configuration
    current_retry: int = 0            # Current retry attempt
```

**Usage:**

```python
from expression.collections import Map

context = ExecutionContext(
    shared_state=Map.of_dict({"input": "data"}),
    params=Map.empty(),
    retry_config=RetryConfig(max_retries=3)
)
```

### NodeResult

Immutable result of node execution:

```python
@dataclass(frozen=True)
class NodeResult:
    value: T                          # Execution result
    action: Action                    # Next action to take
    updated_state: SharedState        # New immutable state
    execution_time: float             # Execution duration
```

### FlowResult

Immutable result of flow execution:

```python
@dataclass(frozen=True)
class FlowResult:
    final_value: T                    # Final execution result
    final_action: Action              # Final action
    final_state: SharedState          # Final immutable state
    execution_path: Block[str]        # Immutable execution path
    total_execution_time: float       # Total execution time
```

## Immutable Collections

### Map (SharedState)

Immutable key-value store for state management:

```python
from expression.collections import Map

# Create empty map
state = Map.empty()

# Add values (returns new map)
new_state = state.add("key", "value")

# Get values safely
value = state.get("key")  # Returns the value directly
exists = state.contains_key("key")  # Returns bool

# Chain operations
final_state = (Map.empty()
    .add("user", "alice")
    .add("count", 42)
    .add("active", True))
```

### Seq

Immutable sequence for ordered data:

```python
from expression.collections import Seq

# Create sequence
items = Seq.of_iterable([1, 2, 3, 4, 5])

# Functional operations
doubled = items.map(lambda x: x * 2)
evens = items.filter(lambda x: x % 2 == 0)
total = items.sum()

# Safe head/tail operations
first = items.try_head()  # Returns Option[T]
rest = items.tail()       # Returns Seq[T]
```

### Block

Immutable list for execution paths and transitions:

```python
from expression.collections import Block

# Create block
path = Block.of("start", "process", "end")

# Add elements (returns new block)
extended_path = path.cons("validate")

# Convert to list for processing
path_list = list(path)
```

## Option and Result Types

### Option[T]

Represents optional values without null:

```python
from expression import Some, Nothing

# Create options
some_value = Some(42)
no_value = Nothing

# Safe operations
result = some_value.map(lambda x: x * 2)  # Some(84)
default = no_value.default_value(0)       # 0

# Pattern matching alternative
if some_value.is_some():
    value = some_value.value
```

### Result[T, E]

Railway-oriented programming for error handling:

```python
from expression import Ok, Error

# Create results
success = Ok("data processed")
failure = Error("validation failed")

# Chain operations
result = (Ok(10)
    .map(lambda x: x * 2)      # Ok(20)
    .bind(lambda x: Ok(x + 5)  # Ok(25)
    if x > 15 else Error("too small")))

# Handle results
if result.is_ok():
    value = result.ok
else:
    error = result.error
```

## Configuration Types

### RetryConfig

Configuration for retry behavior:

```python
@dataclass(frozen=True)
class RetryConfig:
    max_retries: int = 1
    wait_seconds: float = 1.0
    backoff_multiplier: float = 2.0

# Usage
retry_config = RetryConfig(
    max_retries=5,
    wait_seconds=0.5,
    backoff_multiplier=1.5
)
```

### FlowTransition

Immutable flow transition definition:

```python
@dataclass(frozen=True)
class FlowTransition:
    from_node: str
    to_node: str
    action: Action = "default"

# Usage
transition = FlowTransition("validate", "process", "success")
```

## Type Aliases

```python
# Common type aliases
Action = str                          # Action identifier
SharedState = Map[str, Any]          # Immutable state
T = TypeVar('T')                     # Generic type variable
E = TypeVar('E')                     # Error type variable
```

## Working with Immutable Data

### State Updates

Always create new state instead of modifying:

```python
# ❌ Wrong - trying to mutate
def bad_update(state):
    state["key"] = "value"  # Error: Map is immutable
    return state

# ✅ Correct - create new state
def good_update(state):
    return state.add("key", "value")
```

### Chaining Operations

Use method chaining for complex updates:

```python
def process_user_data(state, user_id, name, email):
    return (state
        .add("user_id", user_id)
        .add("name", name)
        .add("email", email)
        .add("processed_at", datetime.now())
        .add("status", "active"))
```

### Safe Access Patterns

Always handle missing values safely:

```python
def get_user_name(state):
    if state.contains_key("user"):
        user = state.get("user")
        if isinstance(user, dict) and "name" in user:
            return user["name"]
    return "Unknown User"
```

## Best Practices

1. **Always Use Immutable Types**: Never use mutable lists, dicts, or objects
2. **Handle Missing Values**: Use `contains_key()` before `get()`
3. **Chain Operations**: Use method chaining for readable transformations
4. **Type Annotations**: Always provide type hints for better safety
5. **Result Types**: Use Result[T, E] for operations that can fail
6. **Option Types**: Use Option[T] for values that might not exist

## Common Patterns

### State Transformation Pipeline

```python
def transform_state(initial_state):
    return (initial_state
        .add("step1", "completed")
        .add("timestamp", time.time())
        .add("processed", True))
```

### Error-Safe Operations

```python
def safe_divide(a, b):
    if b == 0:
        return Error("Division by zero")
    return Ok(a / b)

def calculate(state):
    x = state.get("x") if state.contains_key("x") else 0
    y = state.get("y") if state.contains_key("y") else 1
    
    return safe_divide(x, y).map(
        lambda result: state.add("division_result", result)
    )
```

This type system ensures that ReactiveCodeFlow operations are safe, predictable, and composable while maintaining the benefits of functional programming.
