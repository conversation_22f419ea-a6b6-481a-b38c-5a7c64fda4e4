# Flows: Immutable Workflow Orchestration

Flows orchestrate the execution of multiple nodes through immutable flow definitions and functional composition.

## Flow Architecture

### Flow Definition

A flow is an immutable data structure that defines the workflow:

```python
@dataclass(frozen=True)
class FlowDefinition:
    name: str                           # Flow identifier
    start_node: str                     # Starting node name
    nodes: Map[str, NodeDefinition]     # Immutable node registry
    transitions: Block[FlowTransition]  # Immutable transition rules
```

### Flow Transitions

Transitions define how execution moves between nodes:

```python
@dataclass(frozen=True)
class FlowTransition:
    from_node: str      # Source node
    to_node: str        # Target node
    action: Action      # Triggering action
```

## Creating Flows

### Basic Flow Creation

```python
from reactivecodeflow.core.flow import create_flow, add_node, add_transition

# Create empty flow
flow = create_flow("data_processing", "validate")

# Add nodes
flow = add_node(flow, validation_node)
flow = add_node(flow, transformation_node)
flow = add_node(flow, analysis_node)

# Add transitions
flow = add_transition(flow, "validate", "transform", "success")
flow = add_transition(flow, "transform", "analyze", "transformed")
flow = add_transition(flow, "analyze", "complete", "analyzed")
```

### Functional Flow Composition

```python
def create_data_pipeline():
    """Create a complete data processing pipeline."""
    
    # Create nodes
    validate_node = create_validation_node()
    transform_node = create_transformation_node()
    analyze_node = create_analysis_node()
    
    # Compose flow functionally
    flow = create_flow("data_pipeline", "validate")
    flow = add_node(flow, validate_node)
    flow = add_node(flow, transform_node)
    flow = add_node(flow, analyze_node)
    
    # Define transitions
    flow = add_transition(flow, "validate", "transform", "valid")
    flow = add_transition(flow, "transform", "analyze", "transformed")
    
    return flow
```

## Flow Execution

### Basic Execution

```python
from reactivecodeflow.core.flow import execute_flow
from reactivecodeflow.core.types import ExecutionContext
from expression.collections import Map

# Create execution context
initial_state = Map.of_dict({
    "input_data": [1, 2, 3, 4, 5],
    "config": {"batch_size": 10}
})

context = ExecutionContext(
    shared_state=initial_state,
    params=Map.empty(),
    retry_config=RetryConfig(max_retries=3)
)

# Execute flow
result = execute_flow(flow, context)

if result.is_ok():
    flow_result = result.ok
    print(f"Final state: {flow_result.final_state}")
    print(f"Execution path: {list(flow_result.execution_path)}")
    print(f"Total time: {flow_result.total_execution_time}")
else:
    print(f"Flow failed: {result.error}")
```

### Flow Result Analysis

```python
def analyze_flow_result(flow_result: FlowResult):
    """Analyze the results of flow execution."""
    
    # Check execution path
    expected_path = ["validate", "transform", "analyze"]
    actual_path = list(flow_result.execution_path)
    
    if actual_path == expected_path:
        print("✓ Flow executed expected path")
    else:
        print(f"⚠ Unexpected path: {actual_path}")
    
    # Check final state
    if flow_result.final_state.contains_key("analysis_result"):
        analysis = flow_result.final_state.get("analysis_result")
        print(f"✓ Analysis completed: {analysis}")
    
    # Performance metrics
    if flow_result.total_execution_time < 1.0:
        print("✓ Flow executed within performance threshold")
    else:
        print(f"⚠ Slow execution: {flow_result.total_execution_time}s")
```

## Advanced Flow Patterns

### Conditional Branching

```python
def create_conditional_flow():
    """Create a flow with conditional branching."""
    
    # Create nodes
    input_node = create_input_node()
    validate_node = create_validation_node()
    process_node = create_processing_node()
    error_handler_node = create_error_handler_node()
    output_node = create_output_node()
    
    # Create flow
    flow = create_flow("conditional_flow", "input")
    
    # Add all nodes
    for node in [input_node, validate_node, process_node, error_handler_node, output_node]:
        flow = add_node(flow, node)
    
    # Define conditional transitions
    flow = add_transition(flow, "input", "validate", "ready")
    flow = add_transition(flow, "validate", "process", "valid")
    flow = add_transition(flow, "validate", "error_handler", "invalid")
    flow = add_transition(flow, "process", "output", "processed")
    flow = add_transition(flow, "error_handler", "output", "handled")
    
    return flow
```

### Parallel Processing Flow

```python
def create_parallel_flow():
    """Create a flow that processes data in parallel branches."""
    
    # Create nodes
    splitter_node = create_data_splitter_node()
    branch_a_node = create_branch_processor_node("A")
    branch_b_node = create_branch_processor_node("B")
    merger_node = create_data_merger_node()
    
    # Create flow
    flow = create_flow("parallel_flow", "split")
    
    # Add nodes
    flow = add_node(flow, splitter_node)
    flow = add_node(flow, branch_a_node)
    flow = add_node(flow, branch_b_node)
    flow = add_node(flow, merger_node)
    
    # Define parallel transitions
    flow = add_transition(flow, "split", "process_a", "branch_a")
    flow = add_transition(flow, "split", "process_b", "branch_b")
    flow = add_transition(flow, "process_a", "merge", "completed_a")
    flow = add_transition(flow, "process_b", "merge", "completed_b")
    
    return flow
```

### Loop Flow Pattern

```python
def create_iterative_flow():
    """Create a flow with iterative processing."""
    
    def create_iterator_node():
        def prep_fn(ctx):
            current_iteration = ctx.shared_state.get("iteration") if ctx.shared_state.contains_key("iteration") else 0
            max_iterations = ctx.shared_state.get("max_iterations") if ctx.shared_state.contains_key("max_iterations") else 10
            return Ok({"current": current_iteration, "max": max_iterations})
        
        def exec_fn(iteration_data):
            current = iteration_data["current"]
            max_iter = iteration_data["max"]
            
            if current < max_iter:
                return Ok({"continue": True, "iteration": current + 1})
            else:
                return Ok({"continue": False, "iteration": current})
        
        def post_fn(ctx, prep_res, exec_res):
            updated_state = ctx.shared_state.add("iteration", exec_res["iteration"])
            
            if exec_res["continue"]:
                action = "process"  # Continue loop
            else:
                action = "complete"  # Exit loop
            
            return Ok((action, updated_state))
        
        return create_node("iterator", prep_fn, exec_fn, post_fn)
    
    # Create flow with loop
    flow = create_flow("iterative_flow", "iterator")
    flow = add_node(flow, create_iterator_node())
    flow = add_node(flow, create_processing_node())
    flow = add_node(flow, create_completion_node())
    
    # Define loop transitions
    flow = add_transition(flow, "iterator", "process", "process")
    flow = add_transition(flow, "process", "iterator", "continue")
    flow = add_transition(flow, "iterator", "complete", "complete")
    
    return flow
```

## Flow Composition

### Combining Flows

```python
def compose_flows(flow1: FlowDefinition, flow2: FlowDefinition, connection_action: str = "default") -> FlowDefinition:
    """Compose two flows into a single flow."""
    
    # Find terminal nodes in flow1
    flow1_nodes = set(flow1.nodes.keys())
    flow1_sources = set(t.from_node for t in flow1.transitions)
    terminal_nodes = flow1_nodes - flow1_sources
    
    # Merge nodes
    merged_nodes = flow1.nodes
    for name, node in flow2.nodes.items():
        merged_nodes = merged_nodes.add(name, node)
    
    # Merge transitions
    merged_transitions = flow1.transitions
    for transition in flow2.transitions:
        merged_transitions = merged_transitions.cons(transition)
    
    # Connect flows
    for terminal_node in terminal_nodes:
        connection = FlowTransition(terminal_node, flow2.start_node, connection_action)
        merged_transitions = merged_transitions.cons(connection)
    
    return FlowDefinition(
        name=f"{flow1.name}_composed_{flow2.name}",
        start_node=flow1.start_node,
        nodes=merged_nodes,
        transitions=merged_transitions
    )
```

### Flow Templates

```python
def create_etl_flow_template(extract_node, transform_node, load_node):
    """Template for ETL (Extract, Transform, Load) flows."""
    
    flow = create_flow("etl_flow", "extract")
    flow = add_node(flow, extract_node)
    flow = add_node(flow, transform_node)
    flow = add_node(flow, load_node)
    
    flow = add_transition(flow, "extract", "transform", "extracted")
    flow = add_transition(flow, "transform", "load", "transformed")
    
    return flow

def create_validation_flow_template(validation_rules):
    """Template for data validation flows."""
    
    flow = create_flow("validation_flow", "input")
    
    # Add input node
    flow = add_node(flow, create_input_node())
    
    # Add validation nodes for each rule
    for i, rule in enumerate(validation_rules):
        rule_node = create_validation_rule_node(rule, f"validate_{i}")
        flow = add_node(flow, rule_node)
        
        if i == 0:
            flow = add_transition(flow, "input", f"validate_{i}", "ready")
        else:
            flow = add_transition(flow, f"validate_{i-1}", f"validate_{i}", "valid")
    
    # Add output node
    flow = add_node(flow, create_output_node())
    last_validation = f"validate_{len(validation_rules)-1}"
    flow = add_transition(flow, last_validation, "output", "all_valid")
    
    return flow
```

## Error Handling in Flows

### Flow-Level Error Handling

```python
def create_resilient_flow():
    """Create a flow with comprehensive error handling."""
    
    # Create nodes with error handling
    input_node = create_input_node()
    process_node = create_processing_node()
    error_node = create_error_handler_node()
    retry_node = create_retry_node()
    output_node = create_output_node()
    
    flow = create_flow("resilient_flow", "input")
    
    # Add all nodes
    for node in [input_node, process_node, error_node, retry_node, output_node]:
        flow = add_node(flow, node)
    
    # Normal flow
    flow = add_transition(flow, "input", "process", "ready")
    flow = add_transition(flow, "process", "output", "success")
    
    # Error handling
    flow = add_transition(flow, "process", "error_handler", "error")
    flow = add_transition(flow, "error_handler", "retry", "recoverable")
    flow = add_transition(flow, "error_handler", "output", "fatal")
    flow = add_transition(flow, "retry", "process", "retry")
    
    return flow
```

## Testing Flows

### Unit Testing Flows

```python
def test_data_processing_flow():
    """Test the data processing flow."""
    
    # Create test flow
    flow = create_data_pipeline()
    
    # Create test context
    test_data = [{"id": 1, "value": 10}, {"id": 2, "value": 20}]
    context = ExecutionContext(
        shared_state=Map.of_dict({"input_data": test_data}),
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    # Execute flow
    result = execute_flow(flow, context)
    
    # Assertions
    assert result.is_ok(), f"Flow failed: {result.error if result.is_error() else 'Unknown error'}"
    
    flow_result = result.ok
    assert flow_result.final_action == "complete"
    assert flow_result.final_state.contains_key("processed_data")
    
    # Check execution path
    expected_path = ["validate", "transform", "analyze"]
    actual_path = list(flow_result.execution_path)
    assert actual_path == expected_path
```

### Integration Testing

```python
def test_flow_integration():
    """Integration test for complete flow execution."""
    
    # Test data
    test_cases = [
        {"input": [1, 2, 3], "expected_output": "processed"},
        {"input": [], "expected_error": "empty_input"},
        {"input": None, "expected_error": "null_input"}
    ]
    
    flow = create_data_pipeline()
    
    for test_case in test_cases:
        context = ExecutionContext(
            shared_state=Map.of_dict({"input_data": test_case["input"]}),
            params=Map.empty(),
            retry_config=RetryConfig()
        )
        
        result = execute_flow(flow, context)
        
        if "expected_output" in test_case:
            assert result.is_ok()
            # Verify expected output
        elif "expected_error" in test_case:
            assert result.is_error()
            # Verify expected error
```

## Best Practices

1. **Immutable Flow Definitions**: Never modify flows after creation
2. **Clear Naming**: Use descriptive names for nodes and actions
3. **Single Responsibility**: Each node should have one clear purpose
4. **Error Paths**: Always define error handling transitions
5. **State Validation**: Validate state at each transition
6. **Performance Monitoring**: Track execution times and paths
7. **Testing**: Test both success and failure scenarios
8. **Documentation**: Document flow logic and expected behavior

## Performance Considerations

### Flow Optimization

```python
def optimize_flow_execution(flow: FlowDefinition) -> FlowDefinition:
    """Optimize flow for better performance."""
    
    # Analyze flow structure
    node_count = len(flow.nodes.keys())
    transition_count = len(list(flow.transitions))
    
    print(f"Flow has {node_count} nodes and {transition_count} transitions")
    
    # Identify potential bottlenecks
    # (Implementation would analyze node dependencies and execution patterns)
    
    return flow
```

This functional approach to flows provides predictable, composable, and maintainable workflow orchestration that scales from simple linear processes to complex branching and iterative workflows.
