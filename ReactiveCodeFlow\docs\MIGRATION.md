# Migration Guide: From PocketFlow to ReactiveCodeFlow

This guide provides step-by-step instructions for migrating from the object-oriented PocketFlow framework to the functional ReactiveCodeFlow implementation.

## Overview of Changes

### Architectural Transformation

| Aspect                 | PocketFlow (OOP)             | ReactiveCodeFlow (Functional)                         |
| ---------------------- | ---------------------------- | ----------------------------------------------------- |
| **Data Structures**    | Mutable classes              | Immutable dataclasses with Expression collections     |
| **State Management**   | Mutable dictionary           | Immutable Map from Expression                         |
| **Error Handling**     | Exceptions                   | Result/Option types with railway-oriented programming |
| **Node Definition**    | Class inheritance            | Pure functions with immutable definitions             |
| **Flow Orchestration** | Mutable flow objects         | Immutable flow definitions                            |
| **Execution Model**    | Imperative with side effects | Functional with Expression effects                    |

## Step-by-Step Migration

### 1. Node Migration

#### Original PocketFlow Node

```python
from pocketflow import Node

class AnswerNode(Node):
    def __init__(self, max_retries=3, wait=1):
        super().__init__(max_retries, wait)
    
    def prep(self, shared):
        return shared["question"]
    
    def exec(self, question):
        return call_llm(question)
    
    def post(self, shared, prep_res, exec_res):
        shared["answer"] = exec_res
        return "default"
    
    def exec_fallback(self, prep_res, exc):
        return "Sorry, I couldn't process your question."
```

#### Migrated ReactiveCodeFlow Node

```python
from reactivecodeflow.core.node import create_node
from reactivecodeflow.core.types import RetryConfig
from expression import Ok, Error

def create_answer_node():
    def prep_fn(ctx):
        question = ctx.shared_state.get("question")
        return question.map(Ok).value_or(Error("No question provided"))
    
    def exec_fn(question):
        try:
            return Ok(call_llm(question))
        except Exception as e:
            return Error(str(e))
    
    def post_fn(ctx, prep_res, exec_res):
        updated_state = ctx.shared_state.add("answer", exec_res)
        return Ok(("default", updated_state))
    
    def fallback_fn(prep_res, error):
        return Ok("Sorry, I couldn't process your question.")
    
    return create_node(
        name="answer",
        prep_fn=prep_fn,
        exec_fn=exec_fn,
        post_fn=post_fn,
        fallback_fn=fallback_fn,
        retry_config=RetryConfig(max_retries=3, wait_seconds=1.0)
    )
```

### 2. Flow Migration

#### Original PocketFlow Flow

```python
from pocketflow import Flow

# Create nodes
answer_node = AnswerNode()
end_node = EndNode()

# Connect nodes
answer_node >> end_node

# Create flow
qa_flow = Flow(start=answer_node)

# Execute
shared = {"question": "What is AI?", "answer": None}
qa_flow.run(shared)
print(shared["answer"])
```

#### Migrated ReactiveCodeFlow Flow

```python
from reactivecodeflow.core.flow import create_flow, add_node, add_transition, execute_flow
from reactivecodeflow.core.types import ExecutionContext
from expression.collections import Map

# Create nodes
answer_node = create_answer_node()
end_node = create_end_node()

# Create flow with functional composition
qa_flow = (create_flow("qa_flow", "answer")
           .pipe(lambda f: add_node(f, answer_node))
           .pipe(lambda f: add_node(f, end_node))
           .pipe(lambda f: add_transition(f, "answer", "end")))

# Execute with immutable context
initial_state = Map.of_dict({"question": "What is AI?", "answer": None})
context = ExecutionContext(
    shared_state=initial_state,
    params=Map.empty(),
    retry_config=RetryConfig()
)

result = execute_flow(qa_flow, context)
match result:
    case Ok(flow_result):
        answer = flow_result.final_state.get("answer")
        print(answer.value_or("No answer"))
    case Error(error):
        print(f"Error: {error}")
```

### 3. Batch Processing Migration

#### Original PocketFlow BatchNode

```python
from pocketflow import BatchNode

class CSVProcessor(BatchNode):
    def prep(self, shared):
        return pd.read_csv(shared["input_file"], chunksize=1000)
    
    def exec(self, chunk):
        return {
            "total_sales": chunk["amount"].sum(),
            "num_transactions": len(chunk)
        }
    
    def post(self, shared, prep_res, exec_res_list):
        total_sales = sum(res["total_sales"] for res in exec_res_list)
        shared["statistics"] = {"total_sales": total_sales}
        return "show_stats"
```

#### Migrated ReactiveCodeFlow BatchNode

```python
from reactivecodeflow.core.node import create_batch_node
from expression.collections import Seq

def create_csv_processor():
    def prep_fn(ctx):
        try:
            chunks = pd.read_csv(ctx.shared_state.get("input_file"), chunksize=1000)
            return Ok(Seq.of_iterable(chunks))
        except Exception as e:
            return Error(str(e))
    
    def exec_fn(chunk):
        try:
            return Ok({
                "total_sales": chunk["amount"].sum(),
                "num_transactions": len(chunk)
            })
        except Exception as e:
            return Error(str(e))
    
    def post_fn(ctx, prep_res, exec_res_list):
        total_sales = exec_res_list.map(lambda res: res["total_sales"]).sum()
        statistics = {"total_sales": total_sales}
        updated_state = ctx.shared_state.add("statistics", statistics)
        return Ok(("show_stats", updated_state))
    
    return create_batch_node("csv_processor", prep_fn, exec_fn, post_fn)
```

### 4. Async Migration

#### Original PocketFlow AsyncNode

```python
from pocketflow import AsyncNode

class AsyncAnswerNode(AsyncNode):
    async def prep_async(self, shared):
        return shared["question"]
    
    async def exec_async(self, question):
        return await async_call_llm(question)
    
    async def post_async(self, shared, prep_res, exec_res):
        shared["answer"] = exec_res
        return "default"
```

#### Migrated ReactiveCodeFlow AsyncNode

```python
from reactivecodeflow.core.async_support import create_async_node

def create_async_answer_node():
    async def prep_fn(ctx):
        question = ctx.shared_state.get("question")
        return question.map(Ok).value_or(Error("No question provided"))
    
    async def exec_fn(question):
        try:
            result = await async_call_llm(question)
            return Ok(result)
        except Exception as e:
            return Error(str(e))
    
    async def post_fn(ctx, prep_res, exec_res):
        updated_state = ctx.shared_state.add("answer", exec_res)
        return Ok(("default", updated_state))
    
    return create_async_node("async_answer", prep_fn, exec_fn, post_fn)
```

## Key Migration Patterns

### 1. State Management

- **Before**: `shared["key"] = value` (mutable)
- **After**: `ctx.shared_state.add("key", value)` (immutable)

### 2. Error Handling

- **Before**: `try/except` with exceptions
- **After**: `Result[T, E]` types with railway-oriented programming

### 3. Optional Values

- **Before**: `None` checks
- **After**: `Option[T]` types with `Some`/`Nothing`

### 4. Flow Control

- **Before**: Return strings for actions
- **After**: Return `Result[tuple[Action, SharedState], E]`

### 5. Function Composition

- **Before**: Method chaining on objects
- **After**: Function composition with `pipe` and `compose`

## Benefits After Migration

1. **Immutability**: No accidental state mutations
2. **Type Safety**: Comprehensive type checking
3. **Error Handling**: Explicit error propagation
4. **Testability**: Pure functions are easier to test
5. **Composability**: Natural function composition
6. **Concurrency**: Thread-safe by default
7. **Debugging**: Easier to trace execution flow

## Common Pitfalls and Solutions

### Pitfall 1: Trying to Mutate State

```python
# Wrong - trying to mutate
def post_fn(ctx, prep_res, exec_res):
    ctx.shared_state["key"] = value  # Error!
    
# Correct - return new state
def post_fn(ctx, prep_res, exec_res):
    updated_state = ctx.shared_state.add("key", value)
    return Ok(("default", updated_state))
```

### Pitfall 2: Not Handling Errors Functionally

```python
# Wrong - using exceptions
def exec_fn(data):
    if not data:
        raise ValueError("No data")
    return process(data)

# Correct - using Result types
def exec_fn(data):
    if not data:
        return Error("No data")
    try:
        return Ok(process(data))
    except Exception as e:
        return Error(str(e))
```

### Pitfall 3: Forgetting to Use Expression Collections

```python
# Wrong - using mutable Python collections
def prep_fn(ctx):
    items = [1, 2, 3]  # Mutable list
    return Ok(items)

# Correct - using immutable Expression collections
def prep_fn(ctx):
    items = Seq.of(1, 2, 3)  # Immutable sequence
    return Ok(items)
```

## Testing Migration

Create comprehensive tests to ensure your migration maintains the same functionality:

```python
import pytest
from expression import Ok, Error
from expression.collections import Map

def test_node_migration():
    # Test that migrated node produces same results as original
    original_shared = {"question": "Test question"}
    
    # Original PocketFlow execution
    original_node = OriginalAnswerNode()
    original_result = original_node.run(original_shared)
    
    # ReactiveCodeFlow execution
    migrated_node = create_answer_node()
    context = ExecutionContext(
        shared_state=Map.of_dict({"question": "Test question"}),
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    result = execute_node(migrated_node, context)
    
    match result:
        case Ok(node_result):
            migrated_answer = node_result.updated_state.get("answer")
            assert migrated_answer.value == original_shared["answer"]
        case Error(_):
            pytest.fail("Node execution failed")
```

This migration guide provides a comprehensive roadmap for transforming your PocketFlow applications to use ReactiveCodeFlow's functional programming approach while maintaining all original functionality.
