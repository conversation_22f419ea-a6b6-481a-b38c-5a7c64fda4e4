"""
Computational effects for ReactiveCodeFlow using Expression's effect system.

This module provides effect-based programming constructs for building
reactive workflows with railway-oriented programming patterns.
"""

from typing import Any, Callable, TypeVar, Generator
from collections.abc import Awaitable

from expression import Option, Result, Some, Nothing, Ok, Error
from expression import effect
from expression.collections import Map, Block, Seq

from .types import (
    NodeDefinition, FlowDefinition, ExecutionContext, NodeResult, FlowResult,
    SharedState, Action, T, E
)

# Type variables
T = TypeVar('T')
E = TypeVar('E')
A = TypeVar('A')
B = TypeVar('B')
C = TypeVar('C')

# Node effect for building reactive node pipelines
@effect.result[T, str]()
def node_effect() -> Generator[T, T, T]:
    """
    Effect for building reactive node computations.
    
    This effect enables railway-oriented programming for node execution,
    automatically handling errors and short-circuiting on failures.
    
    Usage:
        @node_effect()
        def my_node_computation():
            data = yield from prep_step()
            result = yield from exec_step(data)
            final = yield from post_step(result)
            yield final
    """
    pass


# Flow effect for building reactive flow pipelines
@effect.result[T, str]()
def flow_effect() -> Generator[T, T, T]:
    """
    Effect for building reactive flow computations.
    
    This effect enables composition of multiple nodes into flows
    with automatic error propagation and state management.
    
    Usage:
        @flow_effect()
        def my_flow_computation():
            result1 = yield from execute_node1()
            result2 = yield from execute_node2(result1)
            yield result2
    """
    pass


# Batch effect for processing sequences
@effect.result[Seq[T], str]()
def batch_effect() -> Generator[Seq[T], Seq[T], Seq[T]]:
    """
    Effect for building reactive batch computations.
    
    This effect enables processing of sequences while maintaining
    functional composition and error handling.
    
    Usage:
        @batch_effect()
        def my_batch_computation():
            items = yield from get_batch_items()
            processed = yield from process_items(items)
            yield processed
    """
    pass


# Option effect for optional computations
@effect.option[T]()
def option_effect() -> Generator[T, T, T]:
    """
    Effect for building optional computations.
    
    This effect short-circuits on Nothing values, enabling
    clean handling of optional data flows.
    
    Usage:
        @option_effect()
        def my_optional_computation():
            value = yield from maybe_get_value()
            processed = yield from maybe_process(value)
            yield processed
    """
    pass


# Utility functions for effect composition

def lift_to_result(fn: Callable[[A], B]) -> Callable[[A], Result[B, str]]:
    """
    Lift a regular function to work with Result types.
    
    Args:
        fn: Function to lift
        
    Returns:
        Function that returns Result[B, str]
    """
    def lifted_fn(value: A) -> Result[B, str]:
        try:
            return Ok(fn(value))
        except Exception as e:
            return Error(str(e))
    
    return lifted_fn


def lift_to_option(fn: Callable[[A], B | None]) -> Callable[[A], Option[B]]:
    """
    Lift a function that may return None to work with Option types.
    
    Args:
        fn: Function that may return None
        
    Returns:
        Function that returns Option[B]
    """
    def lifted_fn(value: A) -> Option[B]:
        result = fn(value)
        return Some(result) if result is not None else Nothing
    
    return lifted_fn


def sequence_options(options: Seq[Option[T]]) -> Option[Seq[T]]:
    """
    Convert a sequence of Options to an Option of sequence.
    
    If any Option is Nothing, returns Nothing.
    If all Options are Some, returns Some with sequence of values.
    
    Args:
        options: Sequence of Option values
        
    Returns:
        Option containing sequence of values or Nothing
    """
    def accumulate(acc: Option[Seq[T]], opt: Option[T]) -> Option[Seq[T]]:
        match (acc, opt):
            case (Some(values), Some(value)):
                return Some(values.cons(value))
            case _:
                return Nothing
    
    return options.fold(accumulate, Some(Seq.empty()))


def sequence_results(results: Seq[Result[T, E]]) -> Result[Seq[T], E]:
    """
    Convert a sequence of Results to a Result of sequence.
    
    If any Result is Error, returns the first Error.
    If all Results are Ok, returns Ok with sequence of values.
    
    Args:
        results: Sequence of Result values
        
    Returns:
        Result containing sequence of values or first error
    """
    def accumulate(acc: Result[Seq[T], E], result: Result[T, E]) -> Result[Seq[T], E]:
        match (acc, result):
            case (Ok(values), Ok(value)):
                return Ok(values.cons(value))
            case (Error(_), _):
                return acc
            case (_, Error(error)):
                return Error(error)
    
    return results.fold(accumulate, Ok(Seq.empty()))


# Effect combinators for building complex workflows

def map_effect(
    effect_fn: Callable[[A], Result[B, E]],
    transform_fn: Callable[[B], C]
) -> Callable[[A], Result[C, E]]:
    """
    Map a transformation function over an effect.
    
    Args:
        effect_fn: Effect function
        transform_fn: Transformation function
        
    Returns:
        New effect function with transformation applied
    """
    def mapped_effect(value: A) -> Result[C, E]:
        return effect_fn(value).map(transform_fn)
    
    return mapped_effect


def bind_effect(
    effect_fn: Callable[[A], Result[B, E]],
    next_effect_fn: Callable[[B], Result[C, E]]
) -> Callable[[A], Result[C, E]]:
    """
    Bind two effects together (monadic bind).
    
    Args:
        effect_fn: First effect function
        next_effect_fn: Second effect function
        
    Returns:
        Combined effect function
    """
    def bound_effect(value: A) -> Result[C, E]:
        return effect_fn(value).bind(next_effect_fn)
    
    return bound_effect


def filter_effect(
    effect_fn: Callable[[A], Result[B, E]],
    predicate: Callable[[B], bool],
    error_msg: str = "Filter condition not met"
) -> Callable[[A], Result[B, E]]:
    """
    Filter an effect based on a predicate.
    
    Args:
        effect_fn: Effect function
        predicate: Predicate function
        error_msg: Error message if predicate fails
        
    Returns:
        Filtered effect function
    """
    def filtered_effect(value: A) -> Result[B, E]:
        result = effect_fn(value)
        match result:
            case Ok(val) if predicate(val):
                return result
            case Ok(_):
                return Error(error_msg)
            case Error(_):
                return result
    
    return filtered_effect


# State management effects

def get_state(key: str) -> Callable[[ExecutionContext], Option[Any]]:
    """
    Get a value from the shared state.
    
    Args:
        key: State key
        
    Returns:
        Function that extracts value from context
    """
    def getter(context: ExecutionContext) -> Option[Any]:
        return context.shared_state.get(key)
    
    return getter


def set_state(key: str, value: Any) -> Callable[[ExecutionContext], ExecutionContext]:
    """
    Set a value in the shared state.
    
    Args:
        key: State key
        value: Value to set
        
    Returns:
        Function that returns updated context
    """
    def setter(context: ExecutionContext) -> ExecutionContext:
        updated_state = context.shared_state.add(key, value)
        return ExecutionContext(
            shared_state=updated_state,
            params=context.params,
            retry_config=context.retry_config,
            current_retry=context.current_retry
        )
    
    return setter


def update_state(key: str, update_fn: Callable[[Any], Any]) -> Callable[[ExecutionContext], ExecutionContext]:
    """
    Update a value in the shared state.
    
    Args:
        key: State key
        update_fn: Function to update the value
        
    Returns:
        Function that returns updated context
    """
    def updater(context: ExecutionContext) -> ExecutionContext:
        current_value = context.shared_state.get(key)
        match current_value:
            case Some(value):
                new_value = update_fn(value)
                updated_state = context.shared_state.add(key, new_value)
                return ExecutionContext(
                    shared_state=updated_state,
                    params=context.params,
                    retry_config=context.retry_config,
                    current_retry=context.current_retry
                )
            case Nothing():
                return context
    
    return updater
