---
description: 
globs: 
alwaysApply: true
---
# Rule: Designate Folders as Read-Only Reference Material

**Description:**
This rule specifies certain project directories that are to be treated as strictly read-only. The AI should use the content of these directories for reference and context only, and must not suggest or perform any modifications within them.

**Affected Directories:**
- `PocketFlow/`
- `tree/`

**Instructions for the AI Agent:**

1.  **Strictly Read-Only:** The directories listed above, along with all their files and subdirectories, are designated as "reference-only" or "read-only."
2.  **No Modifications:** You **MUST NOT** write, edit, delete, refactor, or suggest any changes to any files or content within these specified directories. This includes not generating code that would alter these files.
3.  **No New Content:** You **MUST NOT** create new files or new subdirectories within these specified directories.
4.  **Usage for Reference:** You should use the information contained within these directories solely for understanding context, answering questions, or as a basis for generating code that will reside *outside* of these protected directories.
5.  **Responding to User Requests for Modification:** If a user explicitly or implicitly asks you to modify content within these read-only directories, you must:
    *   Politely decline the request to modify the content in these specific locations.
    *   Clearly state that the targeted directory (or file within it) is designated as read-only reference material.
    *   If appropriate, offer to perform the task in a different, permissible location within the project.

**Example Scenario:**
If a user asks, "Refactor the `helper_function` located in `vendor/external_library/utils.py`", your response should indicate that the `vendor/` directory is read-only and you cannot modify files within it. You might offer to help refactor the function if its code is copied to a user-writeable directory.

**Rationale:**
These directories are intended to hold content such as external libraries, vendor-supplied code, archived documentation, compiled assets, or other critical reference materials. Protecting them from unintentional AI-driven modifications helps maintain project stability, integrity, and adherence to licensing or versioning requirements.

