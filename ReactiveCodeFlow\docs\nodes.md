# Nodes: Pure Functional Execution Units

Nodes are the fundamental execution units in ReactiveCodeFlow, implemented as pure functions with immutable data structures.

## Node Architecture

### Three-Phase Execution

Every node follows a three-phase execution pattern:

1. **Prep Phase**: Prepare data from execution context
2. **Exec Phase**: Execute core logic with prepared data
3. **Post Phase**: Process results and update state

```python
def create_example_node():
    def prep_fn(ctx: ExecutionContext) -> Result[Any, str]:
        # Extract and prepare data from context
        if ctx.shared_state.contains_key("input"):
            return Ok(ctx.shared_state.get("input"))
        return Error("Missing input data")
    
    def exec_fn(prepared_data: Any) -> Result[str, str]:
        # Core business logic
        try:
            result = process_data(prepared_data)
            return Ok(result)
        except Exception as e:
            return Error(str(e))
    
    def post_fn(ctx: ExecutionContext, prep_result: Any, exec_result: str) -> Result[tuple[Action, SharedState], str]:
        # Update state and determine next action
        updated_state = ctx.shared_state.add("output", exec_result)
        return Ok(("success", updated_state))
    
    return create_node("example", prep_fn, exec_fn, post_fn)
```

## Node Types

### Basic Node

Standard node for single-item processing:

```python
from reactivecodeflow.core.node import create_node

def create_validation_node():
    def prep_fn(ctx):
        data = ctx.shared_state.get("raw_data") if ctx.shared_state.contains_key("raw_data") else None
        if data is None:
            return Error("No data to validate")
        return Ok(data)
    
    def exec_fn(data):
        if isinstance(data, dict) and "id" in data:
            return Ok(data)
        return Error("Invalid data format")
    
    def post_fn(ctx, prep_res, exec_res):
        updated_state = ctx.shared_state.add("validated_data", exec_res)
        return Ok(("transform", updated_state))
    
    return create_node("validate", prep_fn, exec_fn, post_fn)
```

### Batch Node

Process multiple items with functional operations:

```python
from reactivecodeflow.core.node import create_batch_node
from expression.collections import Seq

def create_batch_processor():
    def prep_fn(ctx):
        if ctx.shared_state.contains_key("items"):
            items = ctx.shared_state.get("items")
            return Ok(Seq.of_iterable(items))
        return Error("No items to process")
    
    def exec_fn(item):
        # Process single item
        try:
            processed = transform_item(item)
            return Ok(processed)
        except Exception as e:
            return Error(str(e))
    
    def post_fn(ctx, prep_res, exec_results):
        # Aggregate results
        results_list = list(exec_results)
        updated_state = ctx.shared_state.add("processed_items", results_list)
        return Ok(("complete", updated_state))
    
    return create_batch_node("batch_process", prep_fn, exec_fn, post_fn)
```

### Async Node

Asynchronous execution with functional patterns:

```python
from reactivecodeflow.core.async_support import create_async_node

def create_async_api_node():
    async def prep_fn(ctx):
        await asyncio.sleep(0.01)  # Simulate async prep
        if ctx.shared_state.contains_key("api_params"):
            return Ok(ctx.shared_state.get("api_params"))
        return Error("Missing API parameters")
    
    async def exec_fn(params):
        try:
            # Async API call
            response = await make_api_call(params)
            return Ok(response)
        except Exception as e:
            return Error(f"API call failed: {e}")
    
    async def post_fn(ctx, prep_res, exec_res):
        await asyncio.sleep(0.01)  # Simulate async post
        updated_state = ctx.shared_state.add("api_response", exec_res)
        return Ok(("process_response", updated_state))
    
    return create_async_node("api_call", prep_fn, exec_fn, post_fn)
```

## Error Handling and Retry

### Retry Configuration

```python
from reactivecodeflow.core.types import RetryConfig

def create_resilient_node():
    retry_config = RetryConfig(
        max_retries=3,
        wait_seconds=1.0,
        backoff_multiplier=2.0
    )
    
    def prep_fn(ctx):
        return Ok(ctx.shared_state.get("data"))
    
    def exec_fn(data):
        # This might fail and trigger retries
        if random.random() < 0.7:  # 70% failure rate
            return Error("Temporary failure")
        return Ok(f"Processed: {data}")
    
    def post_fn(ctx, prep_res, exec_res):
        updated_state = ctx.shared_state.add("result", exec_res)
        return Ok(("success", updated_state))
    
    def fallback_fn(prep_result, error):
        # Fallback logic when all retries fail
        return Ok(f"Fallback result for: {prep_result}")
    
    return create_node(
        "resilient",
        prep_fn,
        exec_fn,
        post_fn,
        fallback_fn=fallback_fn,
        retry_config=retry_config
    )
```

### Error Propagation

Errors automatically propagate through the pipeline:

```python
def create_error_prone_node():
    def prep_fn(ctx):
        # This error will stop execution
        return Error("Prep failed")
    
    def exec_fn(data):
        # This won't be called if prep fails
        return Ok(data)
    
    def post_fn(ctx, prep_res, exec_res):
        # This won't be called if prep or exec fails
        return Ok(("success", ctx.shared_state))
    
    return create_node("error_prone", prep_fn, exec_fn, post_fn)
```

## Node Execution

### Single Node Execution

```python
from reactivecodeflow.core.node import execute_node
from reactivecodeflow.core.types import ExecutionContext
from expression.collections import Map

# Create node
node = create_validation_node()

# Create context
context = ExecutionContext(
    shared_state=Map.of_dict({"raw_data": {"id": 123, "name": "test"}}),
    params=Map.empty(),
    retry_config=RetryConfig()
)

# Execute node
result = execute_node(node, context)

if result.is_ok():
    node_result = result.ok
    print(f"Action: {node_result.action}")
    print(f"Updated state: {node_result.updated_state}")
    print(f"Execution time: {node_result.execution_time}")
else:
    print(f"Error: {result.error}")
```

### Batch Node Execution

```python
from reactivecodeflow.core.node import execute_batch_node

# Create batch node
batch_node = create_batch_processor()

# Create context with multiple items
context = ExecutionContext(
    shared_state=Map.of_dict({
        "items": [{"id": 1}, {"id": 2}, {"id": 3}]
    }),
    params=Map.empty(),
    retry_config=RetryConfig()
)

# Execute batch node
result = execute_batch_node(batch_node, context)

if result.is_ok():
    batch_result = result.ok
    processed_items = batch_result.value  # Seq of processed items
    print(f"Processed {len(list(processed_items))} items")
```

## Advanced Patterns

### Conditional Execution

```python
def create_conditional_node():
    def prep_fn(ctx):
        return Ok(ctx.shared_state)
    
    def exec_fn(state):
        if state.contains_key("condition") and state.get("condition"):
            return Ok("condition_met")
        return Ok("condition_not_met")
    
    def post_fn(ctx, prep_res, exec_res):
        if exec_res == "condition_met":
            action = "process_further"
        else:
            action = "skip_processing"
        
        updated_state = ctx.shared_state.add("condition_result", exec_res)
        return Ok((action, updated_state))
    
    return create_node("conditional", prep_fn, exec_fn, post_fn)
```

### Data Transformation Pipeline

```python
def create_transform_node():
    def prep_fn(ctx):
        if ctx.shared_state.contains_key("input_data"):
            return Ok(ctx.shared_state.get("input_data"))
        return Error("No input data")
    
    def exec_fn(data):
        # Functional transformation pipeline
        try:
            result = (data
                .strip()
                .lower()
                .replace(" ", "_"))
            return Ok(result)
        except Exception as e:
            return Error(f"Transform failed: {e}")
    
    def post_fn(ctx, prep_res, exec_res):
        updated_state = (ctx.shared_state
            .add("transformed_data", exec_res)
            .add("transform_timestamp", time.time()))
        return Ok(("next_step", updated_state))
    
    return create_node("transform", prep_fn, exec_fn, post_fn)
```

### Aggregation Node

```python
def create_aggregation_node():
    def prep_fn(ctx):
        if ctx.shared_state.contains_key("data_list"):
            return Ok(ctx.shared_state.get("data_list"))
        return Error("No data to aggregate")
    
    def exec_fn(data_list):
        try:
            total = sum(data_list)
            count = len(data_list)
            average = total / count if count > 0 else 0
            
            aggregation = {
                "total": total,
                "count": count,
                "average": average,
                "min": min(data_list) if data_list else 0,
                "max": max(data_list) if data_list else 0
            }
            return Ok(aggregation)
        except Exception as e:
            return Error(f"Aggregation failed: {e}")
    
    def post_fn(ctx, prep_res, exec_res):
        updated_state = ctx.shared_state.add("aggregation_result", exec_res)
        return Ok(("complete", updated_state))
    
    return create_node("aggregate", prep_fn, exec_fn, post_fn)
```

## Best Practices

1. **Keep Functions Pure**: No side effects in prep, exec, or post functions
2. **Handle Errors Gracefully**: Always return Result types from functions
3. **Use Immutable State**: Never modify the input context or state
4. **Validate Inputs**: Check for required data in prep phase
5. **Meaningful Actions**: Use descriptive action names for flow control
6. **Atomic Operations**: Keep exec functions focused on single responsibilities
7. **Resource Cleanup**: Handle resource management in try/except blocks
8. **Type Safety**: Use proper type hints for all function parameters

## Testing Nodes

```python
def test_validation_node():
    node = create_validation_node()
    
    # Test successful case
    context = ExecutionContext(
        shared_state=Map.of_dict({"raw_data": {"id": 123}}),
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    result = execute_node(node, context)
    assert result.is_ok()
    
    node_result = result.ok
    assert node_result.action == "transform"
    assert node_result.updated_state.contains_key("validated_data")
    
    # Test error case
    error_context = ExecutionContext(
        shared_state=Map.empty(),
        params=Map.empty(),
        retry_config=RetryConfig()
    )
    
    error_result = execute_node(node, error_context)
    assert error_result.is_error()
```

This functional approach to nodes provides predictable, testable, and composable execution units that form the foundation of ReactiveCodeFlow workflows.
