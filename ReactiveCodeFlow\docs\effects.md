# Effects: Computational Expressions for Workflows

ReactiveCodeFlow uses the Expression library's effect system to provide computational expressions that handle complex control flow, error handling, and asynchronous operations in a functional manner.

## Effect System Overview

### What are Effects?

Effects are computational expressions that encapsulate side effects and control flow in a purely functional way. They allow you to:

- Handle errors without exceptions
- Manage asynchronous operations
- Compose complex workflows
- Maintain referential transparency

### Core Effect Types

```python
from expression import effect
from expression.core.result import Result

# Result effect for error handling
@effect.result[T, E]()
def computation() -> Generator[T, T, T]:
    # Computation that can fail
    value = yield from some_operation()
    return value

# Option effect for nullable values
@effect.option[T]()
def optional_computation() -> Generator[T, T, T]:
    # Computation that might return None
    value = yield from some_optional_operation()
    return value
```

## Using Effects in ReactiveCodeFlow

### Node Execution with Effects

Node execution uses the result effect to handle errors gracefully:

```python
from expression import effect
from expression.core.result import Result

@effect.result[NodeResult[T], str]()
def execute_node(
    node_def: NodeDefinition[T, E],
    context: ExecutionContext
) -> Generator[NodeResult[T], NodeResult[T], NodeResult[T]]:
    """Execute a node using functional effects."""
    start_time = time.time()
    
    # Preparation phase
    prep_result = yield from node_def.prep_fn(context)
    
    # Execution phase with retry logic
    retry_enabled_exec = with_retry(
        node_def.exec_fn,
        node_def.fallback_fn,
        node_def.retry_config
    )
    exec_result = yield from retry_enabled_exec(prep_result)
    
    # Post-processing phase
    post_result = yield from node_def.post_fn(context, prep_result, exec_result)
    action, updated_state = post_result
    
    execution_time = time.time() - start_time
    
    yield NodeResult(
        value=exec_result,
        action=action,
        updated_state=updated_state,
        execution_time=execution_time
    )
```

### Flow Execution with Effects

Flow orchestration uses effects to manage complex control flow:

```python
@effect.result[FlowResult[Any], str]()
def execute_flow(
    flow_def: FlowDefinition,
    initial_context: ExecutionContext
) -> Generator[FlowResult[Any], FlowResult[Any], FlowResult[Any]]:
    """Execute a flow using functional effects."""
    start_time = time.time()
    execution_path = Block.empty()
    current_context = initial_context
    current_node_name = flow_def.start_node
    last_result = None
    
    while True:
        # Get current node definition
        node_def_option = flow_def.get_node(current_node_name)
        
        if node_def_option.is_some():
            node_def = node_def_option.value
            execution_path = execution_path.cons(current_node_name)
            
            # Execute current node using effects
            node_result = yield from execute_node(node_def, current_context)
            last_result = node_result.value
            
            # Update context
            current_context = ExecutionContext(
                shared_state=node_result.updated_state,
                params=current_context.params,
                retry_config=current_context.retry_config,
                current_retry=0
            )
            
            # Find next node
            next_node_option = get_next_node_name(flow_def, current_node_name, node_result.action)
            
            if next_node_option.is_some():
                current_node_name = next_node_option.value
            else:
                break
        else:
            yield from Error(f"Node '{current_node_name}' not found")
    
    total_time = time.time() - start_time
    
    yield FlowResult(
        final_value=last_result,
        final_action=node_result.action if 'node_result' in locals() else "default",
        final_state=current_context.shared_state,
        execution_path=Block.of(*reversed(list(execution_path))),
        total_execution_time=total_time
    )
```

## Error Handling with Effects

### Railway-Oriented Programming

Effects implement railway-oriented programming automatically:

```python
def create_processing_pipeline():
    """Create a processing pipeline using effects."""
    
    @effect.result[str, str]()
    def pipeline(input_data: str) -> Generator[str, str, str]:
        # Each step can fail, but errors propagate automatically
        validated = yield from validate_input(input_data)
        normalized = yield from normalize_data(validated)
        processed = yield from process_data(normalized)
        formatted = yield from format_output(processed)
        
        return formatted
    
    return pipeline

def validate_input(data: str) -> Result[str, str]:
    if not data:
        return Error("Input cannot be empty")
    return Ok(data)

def normalize_data(data: str) -> Result[str, str]:
    try:
        normalized = data.strip().lower()
        return Ok(normalized)
    except Exception as e:
        return Error(f"Normalization failed: {e}")

def process_data(data: str) -> Result[str, str]:
    if len(data) < 3:
        return Error("Data too short")
    return Ok(f"processed_{data}")

def format_output(data: str) -> Result[str, str]:
    return Ok(f"[{data}]")
```

### Error Recovery Patterns

```python
@effect.result[T, str]()
def with_fallback(
    primary_operation: Callable[[], Result[T, str]],
    fallback_operation: Callable[[], Result[T, str]]
) -> Generator[T, T, T]:
    """Execute primary operation with fallback on failure."""
    
    primary_result = yield from primary_operation()
    
    # If primary fails, try fallback
    if primary_result.is_error():
        fallback_result = yield from fallback_operation()
        return fallback_result
    
    return primary_result

# Usage
@effect.result[str, str]()
def resilient_operation() -> Generator[str, str, str]:
    result = yield from with_fallback(
        lambda: risky_operation(),
        lambda: safe_fallback()
    )
    return result
```

## Async Effects

### Asynchronous Node Execution

```python
async def execute_async_node(
    node_def: AsyncNodeDefinition[T, E],
    context: ExecutionContext
) -> Result[NodeResult[T], str]:
    """Execute an async node with functional effects."""
    start_time = time.time()
    
    try:
        # Async preparation phase
        prep_result = await node_def.prep_fn(context)
        if prep_result.is_error():
            return Error(f"Prep phase failed: {prep_result.error}")
        prep_data = prep_result.ok
        
        # Async execution phase with retry
        retry_enabled_exec = await with_async_retry(
            node_def.exec_fn,
            node_def.fallback_fn,
            node_def.retry_config
        )
        exec_result = await retry_enabled_exec(prep_data)
        if exec_result.is_error():
            return Error(f"Exec phase failed: {exec_result.error}")
        exec_data = exec_result.ok
        
        # Async post-processing phase
        post_result = await node_def.post_fn(context, prep_data, exec_data)
        if post_result.is_error():
            return Error(f"Post phase failed: {post_result.error}")
        action, updated_state = post_result.ok
        
        execution_time = time.time() - start_time
        
        return Ok(NodeResult(
            value=exec_data,
            action=action,
            updated_state=updated_state,
            execution_time=execution_time
        ))
        
    except Exception as e:
        return Error(f"Async node execution failed: {str(e)}")
```

### Async Effect Composition

```python
async def compose_async_operations():
    """Compose multiple async operations."""
    
    async def async_pipeline(input_data):
        # Sequential async operations
        step1 = await async_operation_1(input_data)
        if step1.is_error():
            return step1
        
        step2 = await async_operation_2(step1.ok)
        if step2.is_error():
            return step2
        
        step3 = await async_operation_3(step2.ok)
        return step3
    
    return async_pipeline

async def parallel_async_operations(data_list):
    """Execute async operations in parallel."""
    
    # Create async tasks
    tasks = [async_operation(data) for data in data_list]
    
    # Wait for all to complete
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Convert to Result types
    processed_results = []
    for result in results:
        if isinstance(result, Exception):
            processed_results.append(Error(str(result)))
        else:
            processed_results.append(Ok(result))
    
    return processed_results
```

## Advanced Effect Patterns

### Effect Combinators

```python
def sequence_effects(effects: list) -> Result[list, str]:
    """Sequence a list of effects, stopping on first error."""
    
    @effect.result[list, str]()
    def sequencer() -> Generator[list, list, list]:
        results = []
        for effect_fn in effects:
            result = yield from effect_fn()
            results.append(result)
        return results
    
    return sequencer()

def parallel_effects(effects: list) -> Result[list, str]:
    """Execute effects in parallel (conceptually)."""
    
    results = []
    for effect_fn in effects:
        result = effect_fn()
        if result.is_error():
            return result
        results.append(result.ok)
    
    return Ok(results)
```

### Resource Management with Effects

```python
@effect.result[T, str]()
def with_resource(
    acquire_resource: Callable[[], Result[Resource, str]],
    use_resource: Callable[[Resource], Result[T, str]],
    release_resource: Callable[[Resource], None]
) -> Generator[T, T, T]:
    """Manage resource lifecycle with effects."""
    
    # Acquire resource
    resource = yield from acquire_resource()
    
    try:
        # Use resource
        result = yield from use_resource(resource)
        return result
    finally:
        # Always release resource
        release_resource(resource)

# Usage
def process_with_file(filename: str):
    def acquire():
        try:
            file = open(filename, 'r')
            return Ok(file)
        except Exception as e:
            return Error(f"Failed to open file: {e}")
    
    def use(file):
        try:
            content = file.read()
            processed = process_content(content)
            return Ok(processed)
        except Exception as e:
            return Error(f"Failed to process file: {e}")
    
    def release(file):
        file.close()
    
    return with_resource(acquire, use, release)
```

## Effect Testing

### Testing Effect-Based Code

```python
def test_effect_computation():
    """Test effect-based computations."""
    
    # Test successful case
    @effect.result[int, str]()
    def successful_computation() -> Generator[int, int, int]:
        value = yield from Ok(42)
        doubled = yield from Ok(value * 2)
        return doubled
    
    result = successful_computation()
    assert result.is_ok()
    assert result.ok == 84
    
    # Test error case
    @effect.result[int, str]()
    def failing_computation() -> Generator[int, int, int]:
        value = yield from Error("Something went wrong")
        return value  # This won't be reached
    
    result = failing_computation()
    assert result.is_error()
    assert result.error == "Something went wrong"

def test_async_effects():
    """Test async effect computations."""
    
    async def test_async():
        async def async_computation():
            await asyncio.sleep(0.01)
            return Ok("async result")
        
        result = await async_computation()
        assert result.is_ok()
        assert result.ok == "async result"
    
    asyncio.run(test_async())
```

## Best Practices

1. **Use Effects for Control Flow**: Let effects handle error propagation
2. **Compose Effects**: Build complex operations from simple effects
3. **Handle Resources Properly**: Use effect patterns for resource management
4. **Test Effect Boundaries**: Test both success and failure paths
5. **Avoid Nested Effects**: Keep effect composition flat when possible
6. **Document Effect Behavior**: Clearly document what effects do
7. **Use Type Hints**: Provide clear type annotations for effects
8. **Error Context**: Provide meaningful error messages in effects

## Performance Considerations

### Effect Overhead

Effects have minimal runtime overhead but consider:

```python
# Efficient: Direct computation for simple cases
def simple_operation(x: int) -> Result[int, str]:
    if x < 0:
        return Error("Negative input")
    return Ok(x * 2)

# Use effects for complex control flow
@effect.result[int, str]()
def complex_operation(x: int) -> Generator[int, int, int]:
    validated = yield from validate_input(x)
    processed = yield from process_value(validated)
    formatted = yield from format_result(processed)
    return formatted
```

### Memory Management

Effects use generators which are memory-efficient:

```python
@effect.result[list, str]()
def memory_efficient_processing(large_dataset) -> Generator[list, list, list]:
    """Process large dataset efficiently with effects."""
    
    results = []
    for batch in chunk_data(large_dataset, batch_size=1000):
        batch_result = yield from process_batch(batch)
        results.extend(batch_result)
    
    return results
```

Effects provide a powerful abstraction for managing complex control flow while maintaining the benefits of functional programming. They enable clean, composable, and testable code that handles errors gracefully and scales to complex workflows.
