"""
ReactiveCodeFlow: A functional programming rewrite of PocketFlow Core Framework

This module provides a purely functional approach to workflow orchestration using
the Expression library for functional programming constructs.

Key Features:
- Pure functions with no side effects
- Immutable data structures throughout
- Railway-oriented programming for error handling
- Compositional workflow design
- Type-safe operations with comprehensive type hints

Architecture:
- Uses Expression's Option/Result types for error handling
- Leverages Expression's pipe and compose functions for workflow composition
- Implements immutable state management with Expression's collections
- Provides async support through Expression's async effects
"""

from .core.types import (
    NodeDefinition, BatchNodeDefinition, FlowDefinition, ExecutionContext,
    NodeResult, FlowResult, SharedState, Action, RetryConfig, FlowTransition
)
from .core.node import (
    create_node, create_batch_node, execute_node, execute_batch_node, simple_node
)
from .core.flow import (
    create_flow, add_node, add_transition, execute_flow, pipe_nodes,
    branch_node, compose_flows
)
from .core.effects import (
    node_effect, flow_effect, batch_effect, option_effect,
    lift_to_result, lift_to_option, sequence_options, sequence_results
)
from .core.async_support import (
    AsyncNodeDefinition, AsyncBatchNodeDefinition, create_async_node,
    create_async_batch_node, execute_async_node, execute_async_batch_node,
    simple_async_node
)

__version__ = "0.1.0"
__all__ = [
    # Core types
    "NodeState",
    "FlowState", 
    "ExecutionContext",
    "NodeResult",
    "FlowResult",
    
    # Node functions
    "create_node",
    "create_batch_node",
    "execute_node",
    "execute_batch_node",
    
    # Flow functions
    "create_flow",
    "create_batch_flow",
    "execute_flow",
    "add_transition",
    
    # Effects
    "node_effect",
    "flow_effect",
    "batch_effect",
    
    # Async support
    "create_async_node",
    "create_async_batch_node",
    "create_async_flow",
    "execute_async_node",
    "execute_async_flow",
    
    # Utilities
    "pipe_nodes",
    "compose_flows",
    "retry_with_backoff",
]
