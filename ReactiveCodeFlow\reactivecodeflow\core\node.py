"""
Pure functional node execution for ReactiveCodeFlow.

This module implements node execution using pure functions and immutable data structures.
All operations are side-effect free and use Expression's functional programming constructs.
"""

import time
from typing import Any, Callable, TypeVar
from collections.abc import Generator

from expression import Option, Result, Some, Nothing, Ok, Error, pipe, compose
from expression import effect
from expression.collections import Map, Block, Seq

from .types import (
    NodeDefinition, BatchNodeDefinition, ExecutionContext, NodeResult, 
    RetryConfig, SharedState, Action, T, E
)

# Pure function to create a node definition
def create_node(
    name: str,
    prep_fn: Callable[[ExecutionContext], Result[Any, E]],
    exec_fn: Callable[[Any], Result[T, E]],
    post_fn: Callable[[ExecutionContext, Any, T], Result[tuple[Action, SharedState], E]],
    fallback_fn: Callable[[Any, E], Result[T, E]] | None = None,
    retry_config: RetryConfig = RetryConfig()
) -> NodeDefinition[T, E]:
    """
    Create an immutable node definition.
    
    Args:
        name: Unique identifier for the node
        prep_fn: Pure function for preparation phase
        exec_fn: Pure function for execution phase  
        post_fn: Pure function for post-processing phase
        fallback_fn: Optional fallback function for error handling
        retry_config: Retry configuration
        
    Returns:
        Immutable NodeDefinition
    """
    return NodeDefinition(
        name=name,
        prep_fn=prep_fn,
        exec_fn=exec_fn,
        post_fn=post_fn,
        fallback_fn=fallback_fn,
        retry_config=retry_config
    )


def create_batch_node(
    name: str,
    prep_fn: Callable[[ExecutionContext], Result[Seq[Any], E]],
    exec_fn: Callable[[Any], Result[T, E]],
    post_fn: Callable[[ExecutionContext, Seq[Any], Seq[T]], Result[tuple[Action, SharedState], E]],
    fallback_fn: Callable[[Any, E], Result[T, E]] | None = None,
    retry_config: RetryConfig = RetryConfig()
) -> BatchNodeDefinition[T, E]:
    """
    Create an immutable batch node definition.
    
    Args:
        name: Unique identifier for the batch node
        prep_fn: Pure function returning sequence of items to process
        exec_fn: Pure function to process each item
        post_fn: Pure function for post-processing all results
        fallback_fn: Optional fallback function for error handling
        retry_config: Retry configuration
        
    Returns:
        Immutable BatchNodeDefinition
    """
    return BatchNodeDefinition(
        name=name,
        prep_fn=prep_fn,
        exec_fn=exec_fn,
        post_fn=post_fn,
        fallback_fn=fallback_fn,
        retry_config=retry_config
    )


# Pure function for retry logic using functional composition
def with_retry(
    exec_fn: Callable[[Any], Result[T, E]],
    fallback_fn: Callable[[Any, E], Result[T, E]] | None,
    retry_config: RetryConfig,
    current_retry: int = 0
) -> Callable[[Any], Result[T, E]]:
    """
    Create a retry-enabled version of an execution function.
    
    This is a higher-order function that wraps an execution function
    with retry logic while maintaining purity.
    """
    def retry_wrapper(prep_result: Any) -> Result[T, E]:
        result = exec_fn(prep_result)
        
        if result.is_ok():
            return result
        else:
            error = result.error
            if current_retry < retry_config.max_retries - 1:
                # Simulate wait time (in real implementation, this would be handled externally)
                next_retry = current_retry + 1
                wait_time = retry_config.wait_seconds * (retry_config.backoff_multiplier ** current_retry)

                # Recursive retry with incremented counter
                return with_retry(exec_fn, fallback_fn, retry_config, next_retry)(prep_result)
            else:
                # All retries exhausted, try fallback
                if fallback_fn:
                    return fallback_fn(prep_result, error)
                else:
                    return result
    
    return retry_wrapper


# Pure function to execute a single node using Expression effects
@effect.result[NodeResult[T], str]()
def execute_node(
    node_def: NodeDefinition[T, E], 
    context: ExecutionContext
) -> Generator[NodeResult[T], NodeResult[T], NodeResult[T]]:
    """
    Execute a node using functional effects for railway-oriented programming.
    
    This function uses Expression's effect system to handle the three-phase
    execution (prep -> exec -> post) with automatic error handling.
    """
    start_time = time.time()
    
    # Preparation phase
    prep_result = yield from node_def.prep_fn(context)
    
    # Execution phase with retry logic
    retry_enabled_exec = with_retry(
        node_def.exec_fn,
        node_def.fallback_fn,
        node_def.retry_config
    )
    exec_result = yield from retry_enabled_exec(prep_result)
    
    # Post-processing phase
    post_result = yield from node_def.post_fn(context, prep_result, exec_result)
    action, updated_state = post_result
    
    execution_time = time.time() - start_time
    
    yield NodeResult(
        value=exec_result,
        action=action,
        updated_state=updated_state,
        execution_time=execution_time
    )


# Pure function to execute a batch node
@effect.result[NodeResult[Seq[T]], str]()
def execute_batch_node(
    batch_node_def: BatchNodeDefinition[T, E],
    context: ExecutionContext
) -> Generator[NodeResult[Seq[T]], NodeResult[Seq[T]], NodeResult[Seq[T]]]:
    """
    Execute a batch node processing multiple items functionally.
    
    Uses Expression's sequence operations to process items while
    maintaining immutability and functional composition.
    """
    start_time = time.time()
    
    # Preparation phase - get sequence of items to process
    prep_items = yield from batch_node_def.prep_fn(context)
    
    # Execution phase - process each item with retry logic
    retry_enabled_exec = with_retry(
        batch_node_def.exec_fn,
        batch_node_def.fallback_fn,
        batch_node_def.retry_config
    )
    
    # Use Expression's sequence operations for functional batch processing
    exec_results = prep_items.map(retry_enabled_exec)
    
    # Convert sequence of Results to Result of sequence
    combined_results = sequence_results(exec_results)
    final_results = yield from combined_results
    
    # Post-processing phase
    post_result = yield from batch_node_def.post_fn(context, prep_items, final_results)
    action, updated_state = post_result
    
    execution_time = time.time() - start_time
    
    yield NodeResult(
        value=final_results,
        action=action,
        updated_state=updated_state,
        execution_time=execution_time
    )


# Helper function to sequence Results (convert Seq[Result[T, E]] to Result[Seq[T], E])
def sequence_results(results: Seq[Result[T, E]]) -> Result[Seq[T], E]:
    """
    Convert a sequence of Results into a Result of sequence.

    If any Result is an Error, returns the first Error.
    If all Results are Ok, returns Ok with sequence of values.
    """
    # Collect all values or return first error
    values = []
    for result in results:
        if result.is_ok():
            values.append(result.ok)
        else:
            return result  # Return first error

    return Ok(Seq.of_iterable(values))


# Utility functions for common node patterns
def simple_node(
    name: str,
    exec_fn: Callable[[Any], T],
    prep_fn: Callable[[ExecutionContext], Any] = lambda ctx: ctx.shared_state,
    post_fn: Callable[[ExecutionContext, Any, T], tuple[Action, SharedState]] = lambda ctx, prep, exec: ("default", ctx.shared_state)
) -> NodeDefinition[T, str]:
    """
    Create a simple node with default error handling.
    
    This is a convenience function for creating nodes with minimal boilerplate.
    """
    def safe_prep(ctx: ExecutionContext) -> Result[Any, str]:
        try:
            return Ok(prep_fn(ctx))
        except Exception as e:
            return Error(str(e))
    
    def safe_exec(prep_result: Any) -> Result[T, str]:
        try:
            return Ok(exec_fn(prep_result))
        except Exception as e:
            return Error(str(e))
    
    def safe_post(ctx: ExecutionContext, prep_result: Any, exec_result: T) -> Result[tuple[Action, SharedState], str]:
        try:
            return Ok(post_fn(ctx, prep_result, exec_result))
        except Exception as e:
            return Error(str(e))
    
    return create_node(name, safe_prep, safe_exec, safe_post)
