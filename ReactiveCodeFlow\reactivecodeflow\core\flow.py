"""
Pure functional flow orchestration for ReactiveCodeFlow.

This module implements flow execution using pure functions and immutable data structures.
Flows are composed of nodes and transitions, executed through functional composition.
"""

import time
from typing import Any, Callable, TypeVar
from collections.abc import Generator

from expression import Option, Result, Some, Nothing, Ok, Error, pipe, compose
from expression import effect
from expression.collections import Map, Block, Seq

from .types import (
    FlowDefinition, FlowTransition, NodeDefinition, ExecutionContext, 
    FlowResult, NodeResult, SharedState, Action, T, E
)
from .node import execute_node

# Pure function to create a flow definition
def create_flow(
    name: str,
    start_node: str,
    nodes: dict[str, NodeDefinition] | None = None,
    transitions: list[FlowTransition] | None = None
) -> FlowDefinition:
    """
    Create an immutable flow definition.
    
    Args:
        name: Unique identifier for the flow
        start_node: Name of the starting node
        nodes: Dictionary of node definitions
        transitions: List of flow transitions
        
    Returns:
        Immutable FlowDefinition
    """
    return FlowDefinition(
        name=name,
        start_node=start_node,
        nodes=Map.of_list(list((nodes or {}).items())),
        transitions=Block.of(*(transitions or []))
    )


def add_node(flow_def: FlowDefinition, node_def: NodeDefinition) -> FlowDefinition:
    """
    Add a node to a flow definition (returns new immutable flow).
    
    Args:
        flow_def: Existing flow definition
        node_def: Node definition to add
        
    Returns:
        New FlowDefinition with added node
    """
    updated_nodes = flow_def.nodes.add(node_def.name, node_def)
    return FlowDefinition(
        name=flow_def.name,
        start_node=flow_def.start_node,
        nodes=updated_nodes,
        transitions=flow_def.transitions
    )


def add_transition(
    flow_def: FlowDefinition,
    from_node: str,
    to_node: str,
    action: Action = "default"
) -> FlowDefinition:
    """
    Add a transition to a flow definition (returns new immutable flow).
    
    Args:
        flow_def: Existing flow definition
        from_node: Source node name
        to_node: Target node name
        action: Action that triggers this transition
        
    Returns:
        New FlowDefinition with added transition
    """
    new_transition = FlowTransition(from_node, to_node, action)
    updated_transitions = flow_def.transitions.cons(new_transition)
    return FlowDefinition(
        name=flow_def.name,
        start_node=flow_def.start_node,
        nodes=flow_def.nodes,
        transitions=updated_transitions
    )


# Pure function to find the next node in a flow
def get_next_node_name(
    flow_def: FlowDefinition,
    current_node: str,
    action: Action
) -> Option[str]:
    """
    Find the next node based on current node and action.
    
    Args:
        flow_def: Flow definition
        current_node: Current node name
        action: Action returned by current node
        
    Returns:
        Option containing next node name, or Nothing if no transition found
    """
    matching_transitions = flow_def.transitions.filter(
        lambda t: t.from_node == current_node and t.action == action
    )
    return matching_transitions.try_head().map(lambda t: t.to_node)


# Pure function for flow execution using Expression effects
@effect.result[FlowResult[Any], str]()
def execute_flow(
    flow_def: FlowDefinition,
    initial_context: ExecutionContext
) -> Generator[FlowResult[Any], FlowResult[Any], FlowResult[Any]]:
    """
    Execute a flow using functional effects for railway-oriented programming.
    
    This function orchestrates node execution through the flow graph,
    maintaining immutability and using pure functional composition.
    """
    start_time = time.time()
    execution_path = Block.empty()
    current_context = initial_context
    current_node_name = flow_def.start_node
    last_result = None
    
    while True:
        # Get current node definition
        node_def_option = flow_def.get_node(current_node_name)
        
        if node_def_option.is_some():
            node_def = node_def_option.value
            # Add current node to execution path
            execution_path = execution_path.cons(current_node_name)

            # Execute current node
            node_result = yield from execute_node(node_def, current_context)
            last_result = node_result.value

            # Update context with new state
            current_context = ExecutionContext(
                shared_state=node_result.updated_state,
                params=current_context.params,
                retry_config=current_context.retry_config,
                current_retry=0  # Reset retry counter for next node
            )

            # Find next node based on action
            next_node_option = get_next_node_name(flow_def, current_node_name, node_result.action)

            if next_node_option.is_some():
                current_node_name = next_node_option.value
            else:
                # No more nodes, flow completed
                break
        else:
            # Node not found, this is an error
            yield from Error(f"Node '{current_node_name}' not found in flow '{flow_def.name}'")
    
    total_time = time.time() - start_time
    
    yield FlowResult(
        final_value=last_result,
        final_action=node_result.action if 'node_result' in locals() else "default",
        final_state=current_context.shared_state,
        execution_path=Block.of(*reversed(list(execution_path))),  # Reverse to get correct order
        total_execution_time=total_time
    )


# Pure function to create a batch flow
def create_batch_flow(
    name: str,
    flow_def: FlowDefinition,
    batch_params_fn: Callable[[ExecutionContext], Result[Seq[Map[str, Any]], str]]
) -> Callable[[ExecutionContext], Result[Seq[FlowResult[Any]], str]]:
    """
    Create a batch flow that executes a flow multiple times with different parameters.
    
    Args:
        name: Name of the batch flow
        flow_def: Base flow definition to execute multiple times
        batch_params_fn: Function to generate batch parameters
        
    Returns:
        Function that executes the batch flow
    """
    @effect.result[Seq[FlowResult[Any]], str]()
    def execute_batch_flow(context: ExecutionContext) -> Generator[Seq[FlowResult[Any]], Seq[FlowResult[Any]], Seq[FlowResult[Any]]]:
        # Get batch parameters
        batch_params = yield from batch_params_fn(context)
        
        # Execute flow for each set of parameters
        results = Seq.empty()
        for params in batch_params:
            # Create new context with batch-specific parameters
            batch_context = ExecutionContext(
                shared_state=context.shared_state,
                params=params,
                retry_config=context.retry_config,
                current_retry=0
            )
            
            # Execute flow with batch context
            flow_result = yield from execute_flow(flow_def, batch_context)
            results = results.cons(flow_result)
        
        yield Seq.of_iterable(reversed(list(results)))  # Reverse to maintain order
    
    return execute_batch_flow


# Utility functions for flow composition
def pipe_nodes(*node_names: str) -> list[FlowTransition]:
    """
    Create a sequence of transitions connecting nodes in order.
    
    Args:
        node_names: Names of nodes to connect in sequence
        
    Returns:
        List of FlowTransitions connecting the nodes
    """
    transitions = []
    for i in range(len(node_names) - 1):
        transitions.append(FlowTransition(node_names[i], node_names[i + 1]))
    return transitions


def branch_node(
    from_node: str,
    branches: dict[Action, str]
) -> list[FlowTransition]:
    """
    Create branching transitions from one node to multiple nodes.
    
    Args:
        from_node: Source node name
        branches: Dictionary mapping actions to target nodes
        
    Returns:
        List of FlowTransitions for the branches
    """
    return [
        FlowTransition(from_node, to_node, action)
        for action, to_node in branches.items()
    ]


def compose_flows(
    flow1: FlowDefinition,
    flow2: FlowDefinition,
    connection_action: Action = "default"
) -> FlowDefinition:
    """
    Compose two flows by connecting the end of flow1 to the start of flow2.
    
    Args:
        flow1: First flow
        flow2: Second flow  
        connection_action: Action that connects the flows
        
    Returns:
        New composed FlowDefinition
    """
    # Find terminal nodes in flow1 (nodes with no outgoing transitions)
    flow1_nodes = set(flow1.nodes.keys())
    flow1_sources = set(t.from_node for t in flow1.transitions)
    terminal_nodes = flow1_nodes - flow1_sources
    
    # Merge nodes from both flows
    merged_nodes = flow1.nodes
    for name, node in flow2.nodes.items():
        merged_nodes = merged_nodes.add(name, node)
    
    # Merge transitions and add connections
    merged_transitions = flow1.transitions
    for transition in flow2.transitions:
        merged_transitions = merged_transitions.cons(transition)
    
    # Add transitions from terminal nodes to flow2 start
    for terminal_node in terminal_nodes:
        connection = FlowTransition(terminal_node, flow2.start_node, connection_action)
        merged_transitions = merged_transitions.cons(connection)
    
    return FlowDefinition(
        name=f"{flow1.name}_composed_{flow2.name}",
        start_node=flow1.start_node,
        nodes=merged_nodes,
        transitions=merged_transitions
    )
